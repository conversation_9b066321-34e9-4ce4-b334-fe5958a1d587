import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import type { EventBoostersResponse } from '@/services/openapi'
import {
  claimBattleEventBoosterMutation,
  getBattleEventBoostersQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQueryClient } from '@tanstack/vue-query'

export function useClaimEventBooster() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...claimBattleEventBoosterMutation(),
    onSuccess: (_, variables) => {
      // booster is ready to claim if price is undefined
      const boosterId = variables.body.boosterId
      queryClient.setQueryData(
        getBattleEventBoostersQueryKey(),
        (oldData: EventBoostersResponse) => {
          if (!oldData) return oldData
          const newData: EventBoostersResponse = {
            ...oldData
          }
          newData.boosters.map(booster => {
            if (booster.id === boosterId) {
              return {
                ...booster,
                isClaimed: true
              }
            }
          })
          return newData
        }
      )
      queryClient.invalidateQueries({ queryKey: getBattleEventBoostersQueryKey() })
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    }
  })

  const claim = (boosterId: number) => {
    return mutateAsync({
      body: { boosterId }
    })
  }

  return { claim, isPending }
}
