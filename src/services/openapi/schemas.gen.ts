// This file is auto-generated by @hey-api/openapi-ts

export const AchievementSchema = {
  type: 'object',
  description: 'Represents the current state and progress of an achievement for a player.',
  required: [
    'id',
    'currentLevel',
    'currentProgress',
    'currentRequirement',
    'currentMultiplier',
    'isClaimed',
    'readyToClaim'
  ],
  properties: {
    currentLevel: {
      type: 'integer',
      format: 'int32',
      description: 'Current level the player has reached for this achievement.',
      minimum: 0
    },
    currentMultiplier: {
      type: 'integer',
      format: 'int32',
      description: 'Current reward multiplier for this achievement level.',
      minimum: 0
    },
    currentProgress: {
      type: 'integer',
      format: 'int64',
      description: 'Current progress value toward next level.'
    },
    currentRequirement: {
      type: 'integer',
      format: 'int64',
      description: 'Progress requirement to reach next level.'
    },
    id: {
      type: 'integer',
      format: 'int32',
      description: 'Unique identifier for this achievement type.',
      minimum: 0
    },
    isClaimed: {
      type: 'boolean',
      description: 'Whether this achievement has been fully claimed.'
    },
    readyToClaim: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LevelReadyToClaim'
      },
      description: 'List of achievement levels ready to be claimed.'
    }
  }
} as const

export const AchievementsListResponseSchema = {
  type: 'object',
  description: 'Response containing a list of unattained achievements.',
  required: ['achievements'],
  properties: {
    achievements: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/UnattainedAchievement'
      },
      description: 'The list of unattained achievements.'
    }
  }
} as const

export const AchievementsStateResponseSchema = {
  type: 'object',
  description: 'Response containing a list of achievements and their current status for a player.',
  required: ['achievements'],
  properties: {
    achievements: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Achievement'
      },
      description: 'Vector of all achievements and their progress.'
    }
  }
} as const

export const BattleEventBoosterSchema = {
  type: 'object',
  required: ['id', 'name', 'multiplier', 'currentLevel', 'maxLevel', 'isClaimed'],
  properties: {
    currentLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isClaimed: {
      type: 'boolean'
    },
    maxLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    multiplier: {
      type: 'integer',
      format: 'int64'
    },
    name: {
      type: 'string'
    },
    nextLevelPrice: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/Price'
        }
      ]
    }
  }
} as const

export const BattleEventMeResponseSchema = {
  type: 'object',
  required: ['leagueLevel'],
  properties: {
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    rank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    teamSkin: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    totalScore: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const BattleEventRewardSchema = {
  type: 'object',
  required: ['isWinner', 'teamSkin', 'teamScore', 'enemySkin', 'enemyScore'],
  properties: {
    coinsCollected: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    enemyScore: {
      type: 'integer',
      format: 'int64'
    },
    enemySkin: {
      type: 'integer',
      format: 'int64'
    },
    isWinner: {
      type: 'boolean'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    teamScore: {
      type: 'integer',
      format: 'int64'
    },
    teamSkin: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const BattleEventStateSchema = {
  type: 'object',
  required: ['startedAt', 'endsAt', 'hasMetRequirement', 'hasLeaderboard', 'leagueLock'],
  properties: {
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    hasLeaderboard: {
      type: 'boolean'
    },
    hasMetRequirement: {
      type: 'boolean'
    },
    leagueLock: {
      type: 'boolean'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const BattleLeadersResponseSchema = {
  type: 'object',
  required: ['teamScore', 'enemyTeamScore', 'list'],
  properties: {
    enemyTeamScore: {
      type: 'integer',
      format: 'int64'
    },
    enemyTeamSkin: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/EventLeader'
      }
    },
    teamScore: {
      type: 'integer',
      format: 'int64'
    },
    teamSkin: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const BoostersViewSchema = {
  type: 'object',
  required: ['stackableMagneticField', 'stackableJumper', 'stackableAimbot'],
  properties: {
    stackableAimbot: {
      type: 'integer',
      format: 'int32'
    },
    stackableJumper: {
      type: 'integer',
      format: 'int32'
    },
    stackableMagneticField: {
      type: 'integer',
      format: 'int32'
    },
    timeBoundAimbotsActiveTill: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    timeBoundJumpersActiveTill: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    timeBoundMagneticFieldActiveTill: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    timeBoundMagneticFieldRadius: {
      type: ['integer', 'null'],
      format: 'int32'
    }
  }
} as const

export const CancelWithdrawRequestSchema = {
  type: 'object',
  required: ['withdrawId'],
  properties: {
    withdrawId: {
      type: 'string'
    }
  }
} as const

export const CancelWithdrawResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const CheckTonInvoiceRequestSchema = {
  type: 'object',
  required: ['invoice'],
  properties: {
    invoice: {
      type: 'string'
    }
  }
} as const

export const CheckTonInvoiceResponseSchema = {
  type: 'object',
  required: ['isPaid'],
  properties: {
    isPaid: {
      type: 'boolean'
    }
  }
} as const

export const ClaimAchievementRequestSchema = {
  type: 'object',
  description: 'Request to claim a completed achievement level.',
  required: ['id'],
  properties: {
    id: {
      type: 'integer',
      format: 'int32',
      description: 'ID of the achievement to claim.',
      minimum: 0
    }
  }
} as const

export const ClaimAchievementResponseSchema = {
  type: 'object',
  description: 'Response after claiming an achievement level.',
  required: ['newMultiplier'],
  properties: {
    newMultiplier: {
      type: 'integer',
      format: 'int64',
      description: 'The new total multiplier value after claiming'
    }
  }
} as const

export const ClaimBattleEventBoosterRequestSchema = {
  type: 'object',
  required: ['boosterId'],
  properties: {
    boosterId: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const ClaimBattleEventBoosterResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const ClaimDailyRewardRequestSchema = {
  type: 'object'
} as const

export const ClaimDailyRewardResponseSchema = {
  type: 'object',
  required: ['rewards'],
  properties: {
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    }
  }
} as const

export const ClaimDailyTaskRequestSchema = {
  type: 'object',
  required: ['taskId'],
  properties: {
    taskId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClaimDailyTaskResponseSchema = {
  type: 'object',
  required: ['reward'],
  properties: {
    reward: {
      $ref: '#/components/schemas/RewardInfo'
    }
  }
} as const

export const ClaimFarmingRequestSchema = {
  type: 'object'
} as const

export const ClaimFarmingResponseSchema = {
  type: 'object',
  required: ['farmedTickets', 'leaguesToClaim'],
  properties: {
    farmedTickets: {
      type: 'integer',
      format: 'int64'
    },
    leaguesToClaim: {
      type: 'array',
      items: {
        type: 'integer',
        format: 'int32'
      }
    }
  }
} as const

export const ClaimGlobalTaskRequestSchema = {
  type: 'object',
  required: ['taskId'],
  properties: {
    taskId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClaimGlobalTaskResponseSchema = {
  type: 'object',
  required: ['reward', 'leaguesToClaim'],
  properties: {
    leaguesToClaim: {
      type: 'array',
      items: {
        type: 'integer',
        format: 'int32'
      }
    },
    reward: {
      $ref: '#/components/schemas/RewardInfo'
    }
  }
} as const

export const ClaimLeagueRewardResponseSchema = {
  type: 'object',
  required: ['rewards'],
  properties: {
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    }
  }
} as const

export const ClaimReferralRewardRequestSchema = {
  type: 'object'
} as const

export const ClaimReferralRewardResponseSchema = {
  type: 'object',
  required: ['ticketsClaimed', 'leaguesToClaim'],
  properties: {
    leaguesToClaim: {
      type: 'array',
      items: {
        type: 'integer',
        format: 'int32'
      }
    },
    ticketsClaimed: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClaimSkinRequestSchema = {
  type: 'object',
  required: ['skinId'],
  properties: {
    skinId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClaimSkinResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const ClanEventLeadersResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/EventLeader'
      }
    }
  }
} as const

export const ClanEventMeResponseSchema = {
  type: 'object',
  required: ['leagueLevel'],
  properties: {
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    rank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    ticketsCollected: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const ClanEventRewardSchema = {
  type: 'object',
  required: ['ticketsCollected'],
  properties: {
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    ticketsCollected: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClanEventStateSchema = {
  type: 'object',
  required: ['clanId', 'endsAtEpochSec', 'leagueLock'],
  properties: {
    clanId: {
      type: 'integer',
      format: 'int64'
    },
    endsAtEpochSec: {
      type: 'integer',
      format: 'int64'
    },
    leagueLock: {
      type: 'boolean'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const ClanRatingItemSchema = {
  type: 'object',
  required: ['id', 'name', 'rating', 'membersCount'],
  properties: {
    id: {
      type: 'integer',
      format: 'int64'
    },
    membersCount: {
      type: 'integer',
      format: 'int64'
    },
    name: {
      type: 'string'
    },
    rating: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClanRatingResponseSchema = {
  type: 'object',
  required: ['clanId', 'clanName', 'isClanLeader', 'rating', 'membersCount', 'list'],
  properties: {
    clanId: {
      type: 'integer',
      format: 'int64'
    },
    clanLink: {
      type: ['string', 'null']
    },
    clanName: {
      type: 'string'
    },
    eventEndsAtEpochSec: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    isClanLeader: {
      type: 'boolean'
    },
    isEventPossible: {
      type: ['boolean', 'null']
    },
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/UserClanRatingItem'
      }
    },
    membersCount: {
      type: 'integer',
      format: 'int64'
    },
    rating: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ClansRatingResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ClanRatingItem'
      }
    }
  }
} as const

export const CompleteGlobalTaskRequestSchema = {
  type: 'object',
  required: ['taskId'],
  properties: {
    taskId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const CompleteGlobalTaskResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const ConnectWalletRequestSchema = {
  type: 'object',
  required: ['address'],
  properties: {
    address: {
      type: 'string'
    }
  }
} as const

export const ConnectWalletResponseSchema = {
  type: 'object',
  required: ['ok', 'wallet'],
  properties: {
    ok: {
      type: 'boolean'
    },
    wallet: {
      type: 'string'
    }
  }
} as const

export const ContestRequirementSchema = {
  type: 'string',
  enum: ['tickets', 'friends', 'multiplier', 'skin', 'task', 'starsTotal', 'starsDuringContest']
} as const

export const ContestRequirementStatusSchema = {
  type: 'object',
  required: ['completed', 'requirement', 'value'],
  properties: {
    completed: {
      type: 'boolean'
    },
    requirement: {
      $ref: '#/components/schemas/ContestRequirement'
    },
    value: {
      $ref: '#/components/schemas/ContestRequirementValue'
    }
  }
} as const

export const ContestRequirementValueSchema = {
  oneOf: [
    {
      type: 'string'
    },
    {
      type: 'integer',
      format: 'int64'
    }
  ]
} as const

export const ContestResponseSchema = {
  type: 'object',
  required: [
    'contestId',
    'startsAt',
    'endsAt',
    'participantsAmount',
    'requirementsCompleted',
    'requirements'
  ],
  properties: {
    contestId: {
      type: 'integer',
      format: 'int64'
    },
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    participantsAmount: {
      type: 'integer',
      format: 'int64'
    },
    requirements: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ContestRequirementStatus'
      }
    },
    requirementsCompleted: {
      type: 'boolean'
    },
    startsAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ConvertCustomCoinsRequestSchema = {
  type: 'object'
} as const

export const ConvertCustomCoinsResponseSchema = {
  type: 'object',
  required: ['toncoin'],
  properties: {
    toncoin: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const CreateInvoiceRequestSchema = {
  type: 'object',
  required: ['itemId', 'currency'],
  properties: {
    currency: {
      $ref: '#/components/schemas/ExternalCurrencyType'
    },
    itemId: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const CreateInvoiceResponseSchema = {
  type: 'object',
  required: ['invoice'],
  properties: {
    invoice: {
      type: 'string'
    }
  }
} as const

export const CreateReferralLinkRequestSchema = {
  type: 'object'
} as const

export const CreateReferralLinkResponseSchema = {
  type: 'object',
  required: ['reflink'],
  properties: {
    reflink: {
      type: 'string'
    }
  }
} as const

export const CreateReviveInvoiceByStarsRequestSchema = {
  type: 'object',
  required: ['sessionId'],
  properties: {
    sessionId: {
      type: 'integer',
      format: 'int64',
      minimum: 0
    }
  }
} as const

export const CreateReviveInvoiceByStarsResponseSchema = {
  type: 'object',
  required: ['invoice'],
  properties: {
    invoice: {
      type: 'string'
    }
  }
} as const

export const CreateSessionRequestSchema = {
  type: 'object',
  required: ['session'],
  properties: {
    session: {
      type: 'string'
    }
  }
} as const

export const CreateSessionResponseSchema = {
  type: 'object',
  required: ['session'],
  properties: {
    session: {
      type: 'string'
    }
  }
} as const

export const CreateWithdrawRequestSchema = {
  type: 'object',
  required: ['currency', 'amount'],
  properties: {
    amount: {
      type: 'integer',
      format: 'int64'
    },
    currency: {
      type: 'string'
    }
  }
} as const

export const CreateWithdrawResponseSchema = {
  type: 'object',
  properties: {
    withdraw: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/Withdraw'
        }
      ]
    }
  }
} as const

export const CurrencySchema = {
  type: 'string',
  enum: [
    'stars',
    'ton',
    'soft',
    'hard',
    'usd',
    'magicHorns',
    'dynamicCoins',
    'tickets',
    'puzzleCoins'
  ]
} as const

export const CustomCoinEventMeResponseSchema = {
  type: 'object',
  required: ['leagueLevel'],
  properties: {
    coinsCollected: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    rank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    }
  }
} as const

export const CustomCoinEventRewardSchema = {
  type: 'object',
  properties: {
    coinsCollected: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    }
  }
} as const

export const CustomCoinEventStateSchema = {
  type: 'object',
  required: ['startedAt', 'endsAt', 'hasMetRequirement', 'hasLeaderboard', 'leagueLock'],
  properties: {
    banner: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventBannerType'
        }
      ]
    },
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    hasLeaderboard: {
      type: 'boolean'
    },
    hasMetRequirement: {
      type: 'boolean'
    },
    leagueLock: {
      type: 'boolean'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const CustomCoinLeadersResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/EventLeader'
      }
    }
  }
} as const

export const DailyRewardItemSchema = {
  type: 'object',
  required: ['day', 'claimed'],
  properties: {
    claimed: {
      type: 'boolean'
    },
    day: {
      type: 'integer',
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/RewardInfo'
        }
      ]
    },
    rewards: {
      type: ['array', 'null'],
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    }
  }
} as const

export const DailyRewardsSchema = {
  type: 'object',
  required: ['currentDay', 'rewards', 'milestones'],
  properties: {
    currentDay: {
      type: 'integer',
      format: 'int64'
    },
    invitedRefs: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    milestones: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/DailyRewardItem'
      }
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/DailyRewardItem'
      }
    }
  }
} as const

export const DailyTaskSchema = {
  type: 'object',
  required: ['name', 'taskId', 'reward', 'completed', 'claimed', 'target', 'current'],
  properties: {
    claimed: {
      type: 'boolean'
    },
    completed: {
      type: 'boolean'
    },
    current: {
      type: 'integer',
      format: 'int64'
    },
    name: {
      type: 'string'
    },
    reward: {
      $ref: '#/components/schemas/RewardInfo'
    },
    target: {
      type: 'integer',
      format: 'int64'
    },
    taskId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const DailyTasksResponseSchema = {
  type: 'object',
  required: ['tasks', 'beginningTasks', 'lootboxProgress', 'lootboxAvailable'],
  properties: {
    beginningTasks: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/DailyTask'
      }
    },
    lootboxAvailable: {
      type: 'boolean'
    },
    lootboxProgress: {
      type: 'integer',
      format: 'int32'
    },
    tasks: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/DailyTask'
      }
    }
  }
} as const

export const DailyWriteOffStateSchema = {
  type: 'object',
  properties: {
    heistEndResult: {
      type: ['boolean', 'null']
    },
    mobsToKill: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    timestampToFail: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    tonLockedBalance: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const DisconnectWalletRequestSchema = {
  type: 'object'
} as const

export const DisconnectWalletResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const EventBannerTypeSchema = {
  type: 'string',
  enum: ['skins', 'lootBox']
} as const

export const EventBoostersResponseSchema = {
  type: 'object',
  required: ['boosters'],
  properties: {
    boosters: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/BattleEventBooster'
      }
    }
  }
} as const

export const EventLeaderSchema = {
  type: 'object',
  required: ['id', 'value', 'name', 'leagueLevel'],
  properties: {
    id: {
      type: 'integer',
      format: 'int64'
    },
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    name: {
      type: 'string'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    value: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const EventRewardSchema = {
  type: 'object',
  description: `Represents a reward that can be earned from an event.
Contains the currency type and amount of the reward.`,
  required: ['currency', 'amount'],
  properties: {
    amount: {
      type: 'integer',
      format: 'int64',
      description:
        "The amount of currency to be awarded. For unlimitedLives, it's the number of seconds"
    },
    currency: {
      $ref: '#/components/schemas/EventRewardCurrency',
      description: 'The type of currency for this reward'
    }
  }
} as const

export const EventRewardCurrencySchema = {
  type: 'string',
  description: `Represents the different types of currencies that can be awarded as event rewards.
Used to specify the currency type for event reward responses.`,
  enum: [
    'soft',
    'hard',
    'tickets',
    'ton',
    'unlimitedLives',
    'magicHorns',
    'dynamicCoins',
    'wheelSpins',
    'puzzleCoins'
  ]
} as const

export const EventTypeSchema = {
  type: 'string',
  description: `Represents the different types of events that can be boosted.
Currently only supports Battle events.`,
  enum: ['battle']
} as const

export const ExternalCurrencyTypeSchema = {
  type: 'string',
  enum: ['stars', 'ton']
} as const

export const FortuneWheelConfigResponseSchema = {
  type: 'object',
  description: `Response containing the configuration of the fortune wheel.
Includes information about each sector and its associated reward.`,
  required: ['sectors'],
  properties: {
    sectors: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/FortuneWheelSector'
      },
      description: 'List of sectors on the fortune wheel and their rewards'
    }
  }
} as const

export const FortuneWheelRollResponseSchema = {
  type: 'object',
  description: `Response returned when rolling the fortune wheel.
Contains the sector number that was landed on and the reward that was won.`,
  required: ['sector', 'reward'],
  properties: {
    reward: {
      $ref: '#/components/schemas/RewardInfo',
      description: 'The reward that was won.'
    },
    sector: {
      type: 'integer',
      format: 'int32',
      description: 'The sector number (1-8) that the wheel landed on.',
      minimum: 0
    }
  }
} as const

export const FortuneWheelSectorSchema = {
  type: 'object',
  description: `Represents a single sector on the fortune wheel.
Contains information about the sector number, reward type, and display text.`,
  required: ['sector', 'type', 'displayText'],
  properties: {
    displayText: {
      type: 'string',
      description: "Text to display for this sector's reward"
    },
    sector: {
      type: 'integer',
      format: 'int32',
      description: 'The sector number (1-8) on the wheel',
      minimum: 0
    },
    type: {
      $ref: '#/components/schemas/RewardType',
      description: 'The type of reward for this sector (Tickets or TON)'
    }
  }
} as const

export const GameEventRequirementSchema = {
  oneOf: [
    {
      type: 'object',
      required: ['referrals'],
      properties: {
        referrals: {
          type: 'integer',
          format: 'int64'
        }
      }
    },
    {
      type: 'object',
      required: ['clan'],
      properties: {
        clan: {
          type: 'integer',
          format: 'int64'
        }
      }
    }
  ],
  description: 'Represents requirements that must be met to participate in a game event.'
} as const

export const HotRecordEventRewardSchema = {
  type: 'object',
  properties: {
    highestScore: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    }
  }
} as const

export const HotRecordEventStateSchema = {
  type: 'object',
  required: ['startedAt', 'endsAt', 'hasMetRequirement', 'leagueLock'],
  properties: {
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    hasMetRequirement: {
      type: 'boolean'
    },
    leagueLock: {
      type: 'boolean'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    requirement: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/GameEventRequirement'
        }
      ]
    },
    rewardCurrency: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventRewardCurrency'
        }
      ]
    },
    rewardMaxAmount: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const HotRecordLeadersResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/EventLeader'
      }
    }
  }
} as const

export const HotRecordMeResponseSchema = {
  type: 'object',
  required: ['leagueLevel'],
  properties: {
    highestScore: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    rank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    }
  }
} as const

export const InGameCurrencyTypeSchema = {
  type: 'string',
  enum: ['hard', 'soft', 'tickets', 'magicHorns', 'dynamicCoins', 'puzzleCoins']
} as const

export const LeagueFeatureSchema = {
  type: 'string',
  enum: [
    'farming',
    'dailyReward',
    'hotRecordEvent',
    'onePercentEvent',
    'tonMiningEvent',
    'withdraw',
    'offers',
    'customCoinEvent',
    'lives',
    'dynamicCoins',
    'puzzleCoins',
    'battleEvent',
    'clanEvent'
  ]
} as const

export const LeagueLeaderSchema = {
  type: 'object',
  required: ['id', 'value', 'name'],
  properties: {
    id: {
      type: 'integer',
      format: 'int64'
    },
    name: {
      type: 'string'
    },
    value: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const LeagueLeadersResponseSchema = {
  type: 'object',
  required: ['rewards', 'list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LeagueLeader'
      }
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    }
  }
} as const

export const LeagueListItemSchema = {
  type: 'object',
  required: ['name', 'leagueLevel', 'ticketsRange', 'rewards', 'unlockFeatures'],
  properties: {
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    name: {
      type: 'string'
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    },
    ticketsRange: {
      type: 'array',
      items: {
        type: 'integer',
        format: 'int64'
      }
    },
    unlockFeatures: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LeagueFeature'
      }
    }
  }
} as const

export const LeagueListResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LeagueListItem'
      }
    }
  }
} as const

export const LeagueMeResponseSchema = {
  type: 'object',
  required: ['name', 'leagueLevel', 'rewards', 'leaguesToClaim'],
  properties: {
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    leaguesToClaim: {
      type: 'array',
      items: {
        type: 'integer',
        format: 'int32'
      }
    },
    name: {
      type: 'string'
    },
    rank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    },
    tickets: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const LevelReadyToClaimSchema = {
  type: 'object',
  description: 'Represents an achievement level that is ready to be claimed by the player.',
  required: ['level', 'multiplier', 'requirement'],
  properties: {
    level: {
      type: 'integer',
      format: 'int32',
      description: 'The level number that can be claimed.',
      minimum: 0
    },
    multiplier: {
      type: 'integer',
      format: 'int32',
      description: 'The reward multiplier for this level',
      minimum: 0
    },
    requirement: {
      type: 'integer',
      format: 'int64',
      description: 'The progress requirement that was met'
    }
  }
} as const

export const ListReferralsQuerySchema = {
  type: 'object',
  properties: {
    cursor: {
      type: ['string', 'null']
    }
  }
} as const

export const ListReferralsResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    cursor: {
      type: ['string', 'null']
    },
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Referral'
      }
    }
  }
} as const

export const ListShopResponseSchema = {
  type: 'object',
  required: [
    'friends',
    'hard',
    'soft',
    'lootboxes',
    'stackableBoosters',
    'progressiveOffers',
    'puzzleOffers',
    'wheelSpins'
  ],
  properties: {
    friends: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopItem'
      }
    },
    hard: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopItem'
      }
    },
    lootboxes: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopLootBoxItem'
      }
    },
    progressiveOffers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopProgressiveOffer'
      }
    },
    puzzleOffers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopPuzzleOffer'
      }
    },
    ransom: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/ShopItem'
        }
      ]
    },
    soft: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopItem'
      }
    },
    stackableBoosters: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopStackableBoosterItem'
      }
    },
    wheelSpins: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopItem'
      }
    }
  }
} as const

export const ListSkinsResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Skin'
      }
    }
  }
} as const

export const LoginQuerySchema = {
  type: 'object',
  required: ['initData'],
  properties: {
    initData: {
      type: 'string'
    },
    reflink: {
      type: ['string', 'null']
    }
  }
} as const

export const LoginResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    },
    token: {
      type: ['string', 'null']
    }
  }
} as const

export const LootBoxOfferSchema = {
  type: 'object',
  required: ['id', 'price', 'value'],
  properties: {
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    price: {
      $ref: '#/components/schemas/ShopItemPrice'
    },
    value: {
      type: 'integer',
      format: 'int32'
    }
  }
} as const

export const LootBoxRewardSchema = {
  type: 'object',
  required: ['id', 'description'],
  properties: {
    description: {
      type: 'string'
    },
    id: {
      type: 'integer',
      format: 'int32'
    },
    multiplier: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const LootBoxTypeSchema = {
  type: 'string',
  enum: [
    'rainbowLootBox',
    'luckyLootBox',
    'cryptoLootBox',
    'moonLootBox',
    'easterLootBox',
    'minecraftLootBox',
    'bananaLootBox',
    'starWarsLootBox',
    'infinityLootBox',
    'kungFuLootBox',
    'spongeLootBox',
    'shrekLootBox',
    'justiceLootBox',
    'scoobyLootBox',
    'combatLootBox'
  ]
} as const

export const LootBoxesInfoSchema = {
  type: 'object',
  required: ['freeAvailableAt', 'availableLootboxes'],
  properties: {
    availableLootboxes: {
      type: 'object',
      additionalProperties: {
        type: 'integer',
        format: 'int32'
      },
      propertyNames: {
        type: 'string',
        enum: [
          'rainbowLootBox',
          'luckyLootBox',
          'cryptoLootBox',
          'moonLootBox',
          'easterLootBox',
          'minecraftLootBox',
          'bananaLootBox',
          'starWarsLootBox',
          'infinityLootBox',
          'kungFuLootBox',
          'spongeLootBox',
          'shrekLootBox',
          'justiceLootBox',
          'scoobyLootBox',
          'combatLootBox'
        ]
      }
    },
    freeAvailableAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const OnePercentLeadersResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/EventLeader'
      }
    }
  }
} as const

export const OnePercentMeResponseSchema = {
  type: 'object',
  required: ['leagueLevel'],
  properties: {
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    rank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    totalScore: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const OnepercentEventRewardSchema = {
  type: 'object',
  properties: {
    reward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventReward'
        }
      ]
    },
    totalScore: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const OnepercentEventStateSchema = {
  type: 'object',
  required: ['startedAt', 'endsAt', 'targetTotalScore', 'hasMetRequirement', 'leagueLock'],
  properties: {
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    hasMetRequirement: {
      type: 'boolean'
    },
    leagueLock: {
      type: 'boolean'
    },
    playerRank: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    requirement: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/GameEventRequirement'
        }
      ]
    },
    rewardCurrency: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/EventRewardCurrency'
        }
      ]
    },
    rewardMaxAmount: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    },
    targetTotalScore: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const OpenLootBoxRequestSchema = {
  type: 'object',
  required: ['lootboxType'],
  properties: {
    lootboxType: {
      $ref: '#/components/schemas/LootBoxType'
    }
  }
} as const

export const OpenLootBoxResponseSchema = {
  type: 'object',
  required: ['rewards', 'lootboxType'],
  properties: {
    lootboxType: {
      $ref: '#/components/schemas/LootBoxType'
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LootBoxReward'
      }
    }
  }
} as const

export const PlayerFlagSchema = {
  type: 'string',
  enum: [
    'onepercent_reward_received',
    'hotrecord_reward_received',
    'custom_coin_reward_received',
    'clan_event_reward_received',
    'battle_event_reward_received'
  ]
} as const

export const PlayerProfileResponseSchema = {
  type: 'object',
  required: ['id'],
  properties: {
    created: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    gamesPlayed: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    highestScore: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    id: {
      type: 'integer',
      format: 'int64'
    },
    leagueLevel: {
      type: ['integer', 'null'],
      format: 'int32',
      minimum: 0
    },
    totalScore: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    transactionMade: {
      type: ['boolean', 'null']
    },
    wallet: {
      type: ['string', 'null']
    }
  }
} as const

export const PlayerStateResponseSchema = {
  type: 'object',
  required: ['livesMax', 'liveReviveDuration', 'leagueLevel', 'progressiveOffers', 'puzzleOffers'],
  properties: {
    battleEvent: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/BattleEventState'
        }
      ]
    },
    battleEventReward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/BattleEventReward'
        }
      ]
    },
    boostersView: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/BoostersView'
        }
      ]
    },
    clanEventReward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/ClanEventReward'
        }
      ]
    },
    clanEventState: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/ClanEventState'
        }
      ]
    },
    clanId: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    customCoin: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    customCoinConvertRate: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    customCoinEvent: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/CustomCoinEventState'
        }
      ]
    },
    customCoinEventReward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/CustomCoinEventReward'
        }
      ]
    },
    dailyRewards: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/DailyRewards'
        }
      ]
    },
    dailyWriteOffState: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/DailyWriteOffState'
        }
      ]
    },
    dynamicCoins: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    farming: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/TicketsFarming'
        }
      ]
    },
    hard: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    hasUnclaimedAchievements: {
      type: ['boolean', 'null']
    },
    hasUnclaimedDailyTasks: {
      type: ['boolean', 'null']
    },
    hasUnclaimedTasks: {
      type: ['boolean', 'null']
    },
    hotrecordEvent: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/HotRecordEventState'
        }
      ]
    },
    hotrecordEventReward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/HotRecordEventReward'
        }
      ]
    },
    isBeginnerTonExhausted: {
      type: ['boolean', 'null']
    },
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    leaguesToClaim: {
      type: ['array', 'null'],
      items: {
        type: 'integer',
        format: 'int32'
      }
    },
    liveReviveDuration: {
      type: 'integer',
      format: 'int64'
    },
    lives: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    livesMax: {
      type: 'integer',
      format: 'int64'
    },
    livesRevive: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    livesUnlimitedUntil: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    lootboxesInfo: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/LootBoxesInfo'
        }
      ]
    },
    magicHorns: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    multiplier: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    newTasks: {
      type: ['array', 'null'],
      items: {
        type: 'string'
      }
    },
    onepercentEvent: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/OnepercentEventState'
        }
      ]
    },
    onepercentEventReward: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/OnepercentEventReward'
        }
      ]
    },
    progressiveOffers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ProgressiveOfferInfo'
      }
    },
    puzzleCoins: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    puzzleOffers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/PuzzleOfferInfo'
      }
    },
    reflink: {
      type: ['string', 'null']
    },
    refs: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    refsFake: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    skin: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    soft: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    subscribedToChannel: {
      type: ['boolean', 'null']
    },
    tickets: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    ticketsUnclaimed: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    ton: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    tonOnPlatformEvent: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/TonOnPlatformEventState'
        }
      ]
    },
    tutorial: {
      type: ['boolean', 'null']
    },
    tutorialMobs: {
      type: ['boolean', 'null']
    },
    wheelSpins: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/WheelSpinsInfo'
        }
      ]
    }
  }
} as const

export const PlayerTasksResponseSchema = {
  type: 'object',
  required: ['tasks'],
  properties: {
    tasks: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Task'
      }
    }
  }
} as const

export const PriceSchema = {
  type: 'object',
  required: ['currency', 'amount'],
  properties: {
    amount: {
      type: 'number',
      format: 'float'
    },
    currency: {
      $ref: '#/components/schemas/Currency'
    }
  }
} as const

export const ProgressiveOfferInfoSchema = {
  type: 'object',
  required: ['id', 'startedAt', 'endsAt', 'isCompleted', 'usageDynamicCoins'],
  properties: {
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isCompleted: {
      type: 'boolean'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    },
    usageDynamicCoins: {
      type: 'boolean'
    }
  }
} as const

export const ProgressiveOfferItemSchema = {
  type: 'object',
  required: ['idx', 'rewards', 'isPurchased', 'isAvailable', 'price'],
  properties: {
    idx: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isAvailable: {
      type: 'boolean'
    },
    isPurchased: {
      type: 'boolean'
    },
    price: {
      $ref: '#/components/schemas/Price'
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    }
  }
} as const

export const PurchaseEventBoosterRequestSchema = {
  type: 'object',
  required: ['boosterId', 'event'],
  properties: {
    boosterId: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    event: {
      $ref: '#/components/schemas/EventType'
    }
  }
} as const

export const PurchaseEventBoosterResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    invoiceLink: {
      type: ['string', 'null']
    },
    ok: {
      type: 'boolean'
    }
  }
} as const

export const PurchaseProgressiveOfferRequestSchema = {
  type: 'object',
  required: ['offerId'],
  properties: {
    offerId: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const PurchaseProgressiveOfferResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    invoiceLink: {
      type: ['string', 'null']
    },
    ok: {
      type: 'boolean'
    }
  }
} as const

export const PurchasePuzzleOfferRequestSchema = {
  type: 'object',
  required: ['offerId', 'itemIndex'],
  properties: {
    itemIndex: {
      type: 'integer',
      format: 'int32'
    },
    offerId: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const PurchasePuzzleOfferResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    invoiceLink: {
      type: ['string', 'null']
    },
    ok: {
      type: 'boolean'
    }
  }
} as const

export const PurchaseRequestSchema = {
  type: 'object',
  required: ['itemId', 'currency'],
  properties: {
    currency: {
      $ref: '#/components/schemas/InGameCurrencyType'
    },
    itemId: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const PurchaseResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const PurchaseReviveRequestSchema = {
  type: 'object',
  required: ['sessionId', 'currentScore', 'currency'],
  properties: {
    currency: {
      $ref: '#/components/schemas/RevivalCurrencyType'
    },
    currentScore: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    sessionId: {
      type: 'integer',
      format: 'int64',
      minimum: 0
    }
  }
} as const

export const PurchaseReviveResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const PurchaseSkinRequestSchema = {
  type: 'object',
  required: ['skinId'],
  properties: {
    skinId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const PurchaseSkinResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const PuzzleOfferInfoSchema = {
  type: 'object',
  required: ['id', 'startedAt', 'endsAt', 'isCompleted'],
  properties: {
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isCompleted: {
      type: 'boolean'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const PuzzleOfferItemSchema = {
  type: 'object',
  required: ['idx', 'isPurchased', 'price'],
  properties: {
    idx: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isPurchased: {
      type: 'boolean'
    },
    price: {
      $ref: '#/components/schemas/Price'
    }
  }
} as const

export const ReferralSchema = {
  type: 'object',
  required: ['id', 'name', 'premium', 'leagueLevel'],
  properties: {
    id: {
      type: 'integer',
      format: 'int64'
    },
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    name: {
      type: 'string'
    },
    premium: {
      type: 'boolean'
    },
    ticketsClaimed: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    ticketsUnclaimed: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const RemovePlayerFlagRequestSchema = {
  type: 'object',
  required: ['flag'],
  properties: {
    flag: {
      $ref: '#/components/schemas/PlayerFlag'
    }
  }
} as const

export const RemovePlayerFlagResponseSchema = {
  type: 'object'
} as const

export const RevivalCurrencyTypeSchema = {
  type: 'string',
  description: 'Specifies the type of currency that can be used to purchase a revive.',
  enum: ['hard', 'stars', 'magicHorns']
} as const

export const RevivalPriceSchema = {
  type: 'object',
  description: `Describes the price and currency type required to purchase a revive.
To get more information about \`price\` calculation, see [\`revival_price\`] module.`,
  required: ['price', 'type'],
  properties: {
    price: {
      type: 'integer',
      format: 'int64',
      description: `The amount of currency required, calculated based on the currency type and number of
revives used.`
    },
    type: {
      $ref: '#/components/schemas/RevivalCurrencyType',
      description: 'The type of currency that can be used.'
    }
  }
} as const

export const ReviveInfoRequestSchema = {
  type: 'object',
  required: ['sessionId'],
  properties: {
    sessionId: {
      type: 'integer',
      format: 'int64',
      minimum: 0
    }
  }
} as const

export const ReviveInfoResponseSchema = {
  type: 'object',
  description: 'Response containing information about available revives and their prices.',
  required: [
    'usedRevivalsAmount',
    'maxRevivalsAmount',
    'prices',
    'freeIsAvailable',
    'hardOffers',
    'magicHornOffers'
  ],
  properties: {
    freeIsAvailable: {
      type: 'boolean',
      description: 'Whether the free revive is available.'
    },
    hardOffers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopItem'
      },
      description: `If the user doesn't have enough hard currency, this field will contain
available in-game offers for purchasing hard currency.`
    },
    magicHornOffers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ShopItem'
      },
      description: `If the user doesn't have enough magic horn, this field will contain
available in-game offers for purchasing magic horn.`
    },
    maxRevivalsAmount: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    prices: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RevivalPrice'
      },
      description: `List of prices for the next revive, which may use different currency types. Empty if no
revives are allowed`
    },
    usedRevivalsAmount: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const RewardInfoSchema = {
  type: 'object',
  required: ['type', 'value'],
  properties: {
    multiplier: {
      type: ['integer', 'null'],
      format: 'int64',
      description: 'The multiplier of the reward'
    },
    type: {
      $ref: '#/components/schemas/RewardType',
      description: 'The type of reward (soft currency, hard currency, tickets, or unlimited lives)'
    },
    value: {
      type: 'integer',
      format: 'int64',
      description: 'The value of the reward'
    }
  }
} as const

export const RewardTypeSchema = {
  type: 'string',
  description: 'The different types of rewards that can be earned from tasks',
  enum: [
    'refsFake',
    'soft',
    'hard',
    'tickets',
    'unlimitedLives',
    'ton',
    'dynamicCoins',
    'puzzleCoins',
    'timeBoundMagneticField',
    'fullLives',
    'magicHorns',
    'rainbowLootBox',
    'luckyLootBox',
    'cryptoLootBox',
    'moonLootBox',
    'easterLootBox',
    'minecraftLootBox',
    'bananaLootBox',
    'starWarsLootBox',
    'infinityLootBox',
    'kungFuLootBox',
    'spongeLootBox',
    'shrekLootBox',
    'justiceLootBox',
    'scoobyLootBox',
    'combatLootBox',
    'skin',
    'stackableMagneticField',
    'stackableAimbot',
    'stackableJumper',
    'timeBoundAimbot',
    'timeBoundJumper',
    'lives',
    'customCoin',
    'wheelSpins'
  ]
} as const

export const SelectSkinRequestSchema = {
  type: 'object',
  required: ['skinId'],
  properties: {
    skinId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const SelectSkinResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const ShopItemSchema = {
  type: 'object',
  required: ['id', 'price', 'value'],
  properties: {
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    price: {
      $ref: '#/components/schemas/ShopItemPrice'
    },
    value: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ShopItemPriceSchema = {
  type: 'object',
  required: ['displayPrice', 'prices'],
  properties: {
    displayPrice: {
      $ref: '#/components/schemas/Price'
    },
    prices: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Price'
      }
    }
  }
} as const

export const ShopLootBoxItemSchema = {
  type: 'object',
  required: ['lootboxType', 'offers', 'rewards'],
  properties: {
    availableAt: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    lootboxType: {
      $ref: '#/components/schemas/LootBoxType'
    },
    offers: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LootBoxOffer'
      }
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/LootBoxReward'
      }
    }
  }
} as const

export const ShopMagneticFieldItemSchema = {
  type: 'object',
  required: ['id', 'price', 'radius', 'durationSecs'],
  properties: {
    durationSecs: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    id: {
      type: 'string'
    },
    price: {
      $ref: '#/components/schemas/Price'
    },
    radius: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const ShopProgressiveOfferSchema = {
  type: 'object',
  required: [
    'id',
    'startedAt',
    'endsAt',
    'currentStage',
    'usageDynamicCoins',
    'items',
    'isCompleted'
  ],
  properties: {
    currentStage: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    dynamicCoins: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isCompleted: {
      type: 'boolean'
    },
    items: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/ProgressiveOfferItem'
      }
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    },
    usageDynamicCoins: {
      type: 'boolean'
    }
  }
} as const

export const ShopPuzzleOfferSchema = {
  type: 'object',
  required: [
    'id',
    'startedAt',
    'endsAt',
    'currentStage',
    'puzzleCoins',
    'items',
    'stageGrantPrize',
    'isCompleted'
  ],
  properties: {
    currentStage: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    isCompleted: {
      type: 'boolean'
    },
    items: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/PuzzleOfferItem'
      }
    },
    puzzleCoins: {
      type: 'integer',
      format: 'int64'
    },
    stageGrantPrize: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const ShopStackableBoosterItemSchema = {
  type: 'object',
  required: ['id', 'price', 'type', 'value'],
  properties: {
    id: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    price: {
      $ref: '#/components/schemas/ShopItemPrice'
    },
    type: {
      $ref: '#/components/schemas/StackableBoosterType'
    },
    value: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    }
  }
} as const

export const SkinSchema = {
  type: 'object',
  required: ['id', 'multiplier', 'locked', 'purchased'],
  properties: {
    id: {
      type: 'integer',
      format: 'int64'
    },
    locked: {
      type: 'boolean'
    },
    multiplier: {
      type: 'integer',
      format: 'int64'
    },
    price: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/Price'
        }
      ]
    },
    purchased: {
      type: 'boolean'
    },
    readyToClaim: {
      type: ['boolean', 'null']
    },
    requiresBox: {
      oneOf: [
        {
          type: 'null'
        },
        {
          $ref: '#/components/schemas/LootBoxType'
        }
      ]
    },
    requiresDailyReward: {
      type: ['boolean', 'null']
    },
    requiresRefs: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    requiresTransaction: {
      type: ['boolean', 'null']
    },
    requiresWallet: {
      type: ['boolean', 'null']
    },
    shopItem: {
      type: ['string', 'null']
    }
  }
} as const

export const StackableBoosterTypeSchema = {
  type: 'string',
  description: 'Represents the type of stackable booster available on the client.',
  enum: ['stackableMagneticField', 'stackableJumper', 'stackableAimbot']
} as const

export const StartClanEventResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    endsAtEpochSec: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    ok: {
      type: 'boolean'
    }
  }
} as const

export const StartFarmingRequestSchema = {
  type: 'object'
} as const

export const StartFarmingResponseSchema = {
  type: 'object',
  required: ['startedAt', 'endsAt', 'changeRate'],
  properties: {
    changeRate: {
      type: 'number',
      format: 'double'
    },
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const TaskSchema = {
  type: 'object',
  required: ['name', 'taskId', 'reward', 'completed', 'claimed'],
  properties: {
    claimed: {
      type: 'boolean'
    },
    completed: {
      type: 'boolean'
    },
    name: {
      type: 'string'
    },
    reward: {
      $ref: '#/components/schemas/RewardInfo'
    },
    taskId: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const TicketsFarmingSchema = {
  type: 'object',
  required: ['startedAt', 'endsAt', 'changeRate'],
  properties: {
    changeRate: {
      type: 'number',
      format: 'double'
    },
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    farmedTickets: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    farmedTicketsLastUpdate: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const TonOnPlatformEventStateSchema = {
  type: 'object',
  required: [
    'isTonSpawnEnabled',
    'isTonAvailableForUser',
    'hasEventSkin',
    'startedAt',
    'endsAt',
    'leagueLock'
  ],
  properties: {
    endsAt: {
      type: 'integer',
      format: 'int64'
    },
    hasEventSkin: {
      type: 'boolean'
    },
    isTonAvailableForUser: {
      type: 'boolean'
    },
    isTonSpawnEnabled: {
      type: 'boolean'
    },
    leagueLock: {
      type: 'boolean'
    },
    startedAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const TransactionInfoSchema = {
  type: 'object',
  required: ['id', 'currency', 'amount', 'status', 'rewards', 'createdAt'],
  properties: {
    amount: {
      type: 'integer',
      format: 'int64'
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    currency: {
      $ref: '#/components/schemas/ExternalCurrencyType'
    },
    id: {
      type: 'string'
    },
    rewards: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/RewardInfo'
      }
    },
    status: {
      $ref: '#/components/schemas/TransactionStatus'
    }
  }
} as const

export const TransactionListResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/TransactionInfo'
      }
    }
  }
} as const

export const TransactionStatusSchema = {
  type: 'string',
  enum: ['success', 'failed']
} as const

export const UnattainedAchievementSchema = {
  type: 'object',
  required: ['id', 'currentProgress', 'levels'],
  properties: {
    currentProgress: {
      type: 'integer',
      format: 'int64',
      description: 'The current progress of the achievement.'
    },
    id: {
      type: 'integer',
      format: 'int32',
      description: 'The ID of the achievement.',
      minimum: 0
    },
    levels: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/UnattainedLevel'
      },
      description: 'The levels that have not yet been completed.'
    }
  }
} as const

export const UnattainedLevelSchema = {
  type: 'object',
  required: ['level', 'requirement', 'multiplier'],
  properties: {
    level: {
      type: 'integer',
      format: 'int32',
      description: 'The level number of this achievement tier.',
      minimum: 0
    },
    multiplier: {
      type: 'integer',
      format: 'int32',
      description: 'The reward multiplier for this level.',
      minimum: 0
    },
    requirement: {
      type: 'integer',
      format: 'int64',
      description: 'The amount of progress required to complete this level.'
    }
  }
} as const

export const UpdateSessionRequestSchema = {
  type: 'object',
  required: ['update'],
  properties: {
    update: {
      type: 'string'
    }
  }
} as const

export const UpdateSessionResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const UserClanRatingItemSchema = {
  type: 'object',
  required: ['id', 'rating', 'leagueLevel', 'isOnline'],
  properties: {
    firstName: {
      type: ['string', 'null']
    },
    id: {
      type: 'integer',
      format: 'int64'
    },
    isClanLeader: {
      type: ['boolean', 'null']
    },
    isOnline: {
      type: 'boolean'
    },
    lastName: {
      type: ['string', 'null']
    },
    lastTimeSeen: {
      type: ['integer', 'null'],
      format: 'int32'
    },
    leagueLevel: {
      type: 'integer',
      format: 'int32',
      minimum: 0
    },
    rating: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const UserProfileQuerySchema = {
  type: 'object',
  required: ['id'],
  properties: {
    id: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const UserProfileResponseSchema = {
  type: 'object',
  required: ['id'],
  properties: {
    clanId: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    clanName: {
      type: ['string', 'null']
    },
    firstName: {
      type: ['string', 'null']
    },
    gamesPlayed: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    highestScore: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    id: {
      type: 'integer',
      format: 'int64'
    },
    lastName: {
      type: ['string', 'null']
    },
    leagueLevel: {
      type: ['integer', 'null'],
      format: 'int32',
      minimum: 0
    },
    multiplier: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    skin: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    tickets: {
      type: ['integer', 'null'],
      format: 'int64'
    },
    totalScore: {
      type: ['integer', 'null'],
      format: 'int64'
    }
  }
} as const

export const UtcResponseSchema = {
  type: 'object',
  required: ['utc'],
  properties: {
    utc: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const WalletAssetSchema = {
  type: 'object',
  required: ['currency', 'amount', 'usd', 'withdrawMin'],
  properties: {
    amount: {
      type: 'integer',
      format: 'int64'
    },
    currency: {
      type: 'string'
    },
    usd: {
      type: 'number',
      format: 'double'
    },
    withdrawMin: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const WalletAssetsResponseSchema = {
  type: 'object',
  required: ['usdTotal', 'assets'],
  properties: {
    assets: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/WalletAsset'
      }
    },
    usdTotal: {
      type: 'number',
      format: 'double'
    }
  }
} as const

export const WheelSpinsInfoSchema = {
  type: 'object',
  required: ['amount', 'freeAvailableAt'],
  properties: {
    amount: {
      type: 'integer',
      format: 'int64'
    },
    freeAvailableAt: {
      type: 'integer',
      format: 'int64'
    }
  }
} as const

export const WithdrawSchema = {
  type: 'object',
  required: ['id', 'currency', 'wallet', 'status', 'amount', 'fee', 'updatedAt', 'createdAt'],
  properties: {
    amount: {
      type: 'integer',
      format: 'int64'
    },
    createdAt: {
      type: 'string',
      format: 'date-time'
    },
    currency: {
      type: 'string'
    },
    fee: {
      type: 'integer',
      format: 'int64'
    },
    id: {
      type: 'string'
    },
    status: {
      type: 'string'
    },
    updatedAt: {
      type: 'string',
      format: 'date-time'
    },
    wallet: {
      type: 'string'
    }
  }
} as const

export const WithdrawListResponseSchema = {
  type: 'object',
  required: ['list'],
  properties: {
    list: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Withdraw'
      }
    }
  }
} as const

export const WriteOffClaimResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    }
  }
} as const

export const WriteOffRansomDeniedResponseSchema = {
  type: 'object',
  required: ['ok'],
  properties: {
    ok: {
      type: 'boolean'
    },
    reason: {
      type: ['string', 'null']
    }
  }
} as const
