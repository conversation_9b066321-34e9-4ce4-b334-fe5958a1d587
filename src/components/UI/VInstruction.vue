<script setup lang="ts">
import VOverlay from '@/components/VOverlay.vue'
import { useInstructionStore } from '@/stores/instructionStore.ts'
import {
  INSTRUCTION_TYPE_TO_HEADER,
  INSTRUCTION_STEPS,
} from '@/constants/instructions'
import { useI18n } from 'vue-i18n'
import arrow from '@/assets/images/temp/instructions/arrow.png'
import VButton from '@/components/UI/VButton.vue'

const { t } = useI18n()

const store = useInstructionStore()

const onClick = () => {
  store.hideInstruction()
}

const instructionSteps = () => {
  return store.instructionType ? INSTRUCTION_STEPS[store.instructionType] : []
}
</script>

<template>
  <VOverlay
    class="instruction-overlay"
    :is-open="store.instructionType !== undefined"
  >
    <div class="instruction" @click="onClick">
      <img
        v-if="store.instructionType"
        class="w-[200px]"
        :src="INSTRUCTION_TYPE_TO_HEADER[store.instructionType]"
        alt="skin event title"
      />
      <div
        v-if="store.instructionType"
        class="instruction__steps-container"
        :style="{ '--steps-count': instructionSteps().length }"
      >
        <div
          v-for="step, index in instructionSteps()"
          :key="index"
          class="instruction__step"
          :class="step.class"
          :style="{ '--step': index + 1 }"
        >
          <img
            class="absolute object-contain w-full h-full"
            :src="step.image"
            alt="image"
          />
          <img
            v-if="step.arrowClass"
            :class="step.arrowClass"
            :src="arrow"
            alt="arrow"
          />
          <div
            v-if="step.text"
            class="instruction__step-text text-shadow text-shadow_black text-shadow_thin"
            :class="step.textClass ?? ''"
            v-html="t(step.text)"
          >
          </div>
        </div>
      </div>
      <VButton
        v-if="store.instructionButton !== null"
        class="instruction__button"
        type="success"
        :text="store.instructionButton.text"
        @click="() => store.instructionButton!.onClick()"
      />
      <div v-else class="instruction__tap-to-continue">
        {{ t('actions.tapToContinue') }}
      </div>
    </div>
  </VOverlay>
</template>

<style lang="scss">
.instruction-overlay {
  background: rgba(0, 0, 0, 0.85);
}

// used not as classs for overlay because animation duration affects overlay
.instruction {
  --animation-total-duration: 3.5s;
  position: inherit;
  top: inherit;
  right: inherit;
  bottom: inherit;
  left: inherit;
  padding: max(var(--inset-top), 40px) 0 40px 0 !important;
  
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  row-gap: 20px;

  pointer-events: none;
  animation: instruction-click-event 0.5s forwards;
  animation-delay: var(--animation-total-duration);

  &__steps-container {
    --steps-count: 0;

    position: relative;
    width: 100%;
    max-width: 375px;
    flex: 1;
  }

  &__step {
    opacity: 0;
    position: absolute;

    animation: instruction-appear 0.5s forwards;
    animation-delay: calc(var(--animation-total-duration) / var(--steps-count) * var(--step));

    &-text {
      width: 100%;
      position: absolute;
      font-size: 14px;
      text-align: center;

      &_yellow {
        color: #FFE02F;
      }

      &_blue {
        color: #6DB0ED;
      }
    }
  }
  
  &__footer {
    position: absolute;
  }
  
  &__tap-to-continue {
    opacity: 0;
    animation: instruction-appear 0.5s forwards, instruction-pulse 1s infinite;
    animation-delay: var(--animation-total-duration);

    color: #fbf3dd;
    text-align: center;
    font-size: 36px;
    font-weight: 800;
    line-height: 50px;
  }
  
  &__button {
    width: 80% !important;
    max-width: 340px;
    opacity: 0;
    animation: instruction-appear 0.5s forwards;
    animation-delay: var(--animation-total-duration);
  }

  @keyframes instruction-pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes instruction-appear {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes instruction-click-event {
    0% {
      pointer-events: none;
    }
    100% {
      pointer-events: auto;
    }
  }
}
</style>
