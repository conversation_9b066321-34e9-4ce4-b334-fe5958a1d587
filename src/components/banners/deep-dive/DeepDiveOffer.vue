<script setup lang="ts">
import { useShopItems } from '@/services/client/useShopItems'
import type { ProgressiveOfferItem, RewardType } from '@/services/openapi/types.gen'
import { formatNumberWithSeparator } from '@/utils/number'
import { computed, useTemplateRef, watch } from 'vue'
import LoaderText from '../../LoaderText.vue'
import VButton from '../../UI/VButton.vue'

import chainMediumImage from '@/assets/images/temp/locks/chain-medium.png'
import lockImage from '@/assets/images/temp/locks/lock.png'

import moonBannerImage from '@/assets/images/temp/deep-dive/banner-moon.png'
import oceanBannerImage from '@/assets/images/temp/deep-dive/banner-ocean.png'
import moonMapImage from '@/assets/images/temp/deep-dive/map-moon.png'
import oceanMapImage from '@/assets/images/temp/deep-dive/map-ocean.png'

import { REWARD_TO_IMAGE } from '@/composables/useIconImage'

import jumperAimBoostersImage from '@/assets/images/temp/big-icons/aim-jumper.png'
import threeBoostersImage from '@/assets/images/temp/big-icons/magnet-aim-jumper.png'
import magnetAimBoostersImage from '@/assets/images/temp/big-icons/magnet-aim.png'
import magnetJumperBoostersImage from '@/assets/images/temp/big-icons/magnet-jumper.png'

import RewardItem from '@/components/RewardItem.vue'
import { useIconImage } from '@/composables/useIconImage'
import { usePurchase } from '@/composables/usePurchase'
import { usePlayerState } from '@/services/client/usePlayerState'
import { useToast } from '@/stores/toastStore'
import { useI18n } from 'vue-i18n'
import CountdownTimerManual from '../../UI/CountdownTimerManual.vue'

const REWARD_CLAIMED_CLASS = 'deep-dive-banner__step_available'

const { t } = useI18n()
const { getCurrencyImageClass } = useIconImage()

const EVENT_ID_TO_BANNER_IMAGE: Record<number, string> = {
  10000: moonBannerImage,
  10001: oceanBannerImage
}

const EVENT_ID_TO_MAP_IMAGE: Record<number, string> = {
  10000: moonMapImage,
  10001: oceanMapImage
}

defineProps<{
  days?: number
  hours?: number
  minutes: number
  seconds: number
}>()

const emit = defineEmits(['close'])

const closeBanner = () => {
  emit('close')
}

const { showToast } = useToast()

const { shopItems, isLoading } = useShopItems()
const { playerState } = usePlayerState()
const progressiveOffer = computed(() =>
  shopItems.value?.progressiveOffers.find(offer => offer.usageDynamicCoins && !offer.isCompleted)
)
const rewards = computed(() => progressiveOffer.value?.items.slice(0, -1) ?? [])
const grandPrize = computed(() => progressiveOffer.value?.items.at(-1) ?? null)

const rewardRefs = useTemplateRef('rewardRefs')

const scrollToReward = () => {
  setTimeout(() => {
    if (!rewardRefs.value) return
    const rewardEl = rewardRefs.value!.find(el => el.classList.contains(REWARD_CLAIMED_CLASS))
    if (rewardEl) {
      rewardEl.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      })
    }
  }, 200)
}

watch(
  rewardRefs,
  () => {
    scrollToReward()
  },
  { immediate: true }
)

const isBoosterType = (type: string): boolean => {
  return [
    'stackableMagneticField',
    'stackableAimbot',
    'stackableJumper',
    'timeBoundMagneticField',
    'timeBoundAimbot',
    'timeBoundJumper'
  ].includes(type)
}

const getRewardImage = (rewards: Array<{ type: RewardType }>): string => {
  if (!rewards || rewards.length <= 1) {
    return REWARD_TO_IMAGE[rewards[0]?.type] ?? ''
  }

  const allBoosters = rewards.every(reward => isBoosterType(reward.type))
  if (!allBoosters) {
    return REWARD_TO_IMAGE[rewards[0]?.type] ?? ''
  }

  const hasMagnet = rewards.some(
    reward => reward.type === 'stackableMagneticField' || reward.type === 'timeBoundMagneticField'
  )
  const hasAim = rewards.some(
    reward => reward.type === 'stackableAimbot' || reward.type === 'timeBoundAimbot'
  )
  const hasJumper = rewards.some(
    reward => reward.type === 'stackableJumper' || reward.type === 'timeBoundJumper'
  )

  if (hasMagnet && hasAim && hasJumper) {
    return threeBoostersImage
  } else if (hasMagnet && hasJumper) {
    return magnetJumperBoostersImage
  } else if (hasMagnet && hasAim) {
    return magnetAimBoostersImage
  } else if (hasJumper && hasAim) {
    return jumperAimBoostersImage
  }

  // Fallback to the first booster's image
  return REWARD_TO_IMAGE[rewards[0]?.type] || ''
}

const { purchaseProgressive, isPendingPurchaseProgressive } = usePurchase()

const purhcase = (item: ProgressiveOfferItem) => {
  purchaseProgressive(progressiveOffer.value!.id, item.price!, item.rewards)
    .then(scrollToReward)
    .catch(reason => {
      if (reason.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
      }
    })
}
</script>

<template>
  <div class="sliced-window deep-dive-banner">
    <img
      class="sliced-window__banner"
      :src="EVENT_ID_TO_BANNER_IMAGE[progressiveOffer?.id ?? 10000]"
      alt="event banner"
    />
    <div class="close-button" @click="closeBanner"></div>
    <div class="sliced-window__top">
      <p class="text-[14px] text-shadow text-shadow_black text-shadow_thin text-center mb-[7px]">
        {{ t('deepDiveOffer.description') }}
      </p>
      <div
        v-if="grandPrize"
        class="bg-[#00348F33] flex justify-evenly items-center w-full h-[92px] rounded-[5px]"
      >
        <RewardItem
          v-for="reward in grandPrize.rewards"
          :key="reward.type"
          class="w-[25%] h-[65%]"
          value-class="translate-y-[9px]"
          :image="REWARD_TO_IMAGE[reward.type] ?? ''"
          :type="reward.type"
          :amount="reward.value"
        />
      </div>
      <div class="sliced-window__timer">
        <span v-text="t('endsIn')"></span>
        <CountdownTimerManual
          class="text-[10px] leading-[14px] text-white"
          :days="days"
          :hours="hours"
          :minutes="minutes"
          :seconds="seconds"
        />
      </div>
    </div>
    <div class="sliced-window__top-placeholder"></div>
    <div class="sliced-window__bottom">
      <div class="sliced-window__balance">
        <div class="icon-bg !w-6 !h-6" :class="getCurrencyImageClass('dynamicCoins')"></div>
        <span class="text-shadow text-shadow_black">
          {{ formatNumberWithSeparator(playerState?.dynamicCoins ?? 0) }}
        </span>
      </div>
      <div
        class="deep-dive-banner__bottom-inner"
        :style="{ '--deep-steps-amount': rewards.length }"
      >
        <img
          class="deep-dive-banner__map"
          :src="EVENT_ID_TO_MAP_IMAGE[progressiveOffer?.id ?? 10000]"
          alt="event map"
        />
        <div v-if="grandPrize" class="deep-dive-banner__grand-prize">
          <div class="absolute bottom-0 w-full z-10" v-if="!grandPrize.isPurchased">
            <VButton
              class="!w-full"
              :image-class="getCurrencyImageClass(grandPrize.price!.currency)"
              :text="formatNumberWithSeparator(grandPrize.price!.amount)"
              type="success"
              size="small"
              @click="() => grandPrize?.isAvailable && purhcase(grandPrize)"
            >
            </VButton>
            <template v-if="!grandPrize.isAvailable">
              <img
                class="z-10 league-blocker__locks w-[55%] -top-[10%] absolute left-1/2 -translate-x-[95%]"
                :src="chainMediumImage"
                alt="chain"
              />
              <img
                class="z-10 league-blocker__locks w-[55%] -top-[10%] absolute right-1/2 translate-x-[95%] -scale-x-100"
                :src="chainMediumImage"
                alt="chain"
              />
              <img
                class="z-10 league-blocker__locks w-[17%] top-[62%] absolute left-1/2 -translate-x-1/2"
                :src="lockImage"
                alt="chain"
              />
            </template>
          </div>
          <div v-else class="check-icon purchased-mark"></div>
        </div>
        <div
          v-for="(reward, index) in rewards"
          :key="reward.idx"
          :style="{ '--deep-step': index + 1 }"
          class="deep-dive-banner__step"
          :class="{ [REWARD_CLAIMED_CLASS]: reward.isAvailable }"
          ref="rewardRefs"
        >
          <RewardItem
            class="!absolute top-[15%] left-[48%] w-full h-[45%] -translate-x-1/2 -translate-y-full"
            :type="reward.rewards[0].type"
            :amount="reward.rewards[0].value"
            :image="getRewardImage(reward.rewards)"
            valueClass="-bottom-[29%] translate-y-[9px] text-shadow_thin"
          />
          <div class="absolute bottom-[30px] w-full z-10" v-if="!reward.isPurchased">
            <VButton
              class="!w-full"
              :image-class="getCurrencyImageClass(reward.price.currency)"
              :text="formatNumberWithSeparator(reward.price.amount)"
              type="success"
              size="small"
              @click="() => reward.isAvailable && purhcase(reward)"
            >
            </VButton>
            <template v-if="!reward.isAvailable">
              <img
                class="z-10 league-blocker__locks w-[55%] -top-[10%] absolute left-1/2 -translate-x-[95%]"
                :src="chainMediumImage"
                alt="chain"
              />
              <img
                class="z-10 league-blocker__locks w-[55%] -top-[10%] absolute right-1/2 translate-x-[95%] -scale-x-100"
                :src="chainMediumImage"
                alt="chain"
              />
              <img
                class="z-10 league-blocker__locks w-[17%] top-[62%] absolute left-1/2 -translate-x-1/2"
                :src="lockImage"
                alt="lock"
              />
            </template>
          </div>
          <div v-else class="check-icon purchased-mark"></div>
        </div>
      </div>
    </div>
    <LoaderText
      class="sliced-window__loading"
      :class="{
        'sliced-window__loading_active': isLoading || isPendingPurchaseProgressive
      }"
      :is-loading="isLoading || isPendingPurchaseProgressive"
    />
  </div>
</template>

<style lang="scss">
.deep-dive-banner {
  &__map {
    width: 100%;
  }

  &__bottom-inner {
    pointer-events: auto;
    position: relative;
    z-index: 0;
    width: 100%;
    --deep-steps-amount: 1;
    --deep-steps-padding-bottom: 5.3%;
    --deep-steps-padding-top: 23%;
  }

  &__grand-prize {
    position: absolute;
    width: 30%;
    z-index: 2;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);

    .purchased-mark {
      transform: translate(-50%, -70%) !important;
    }
  }

  &__step {
    --deep-step: 1;
    position: absolute;
    height: 10%;
    width: 30%;
    z-index: 2;

    transform: translateY(100%);
    bottom: calc(
      (100% - var(--deep-steps-padding-bottom) - var(--deep-steps-padding-top)) /
        var(--deep-steps-amount) * var(--deep-step) + var(--deep-steps-padding-bottom)
    );

    &:nth-child(2n) {
      right: 7%;
    }

    &:nth-child(2n + 1) {
      left: 8%;
    }
  }

  .purchased-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -25%);
  }
}
</style>
