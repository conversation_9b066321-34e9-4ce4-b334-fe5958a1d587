<script setup lang="ts">
import { computed, ref } from 'vue'

import BalanceItem from '@/components/UI/BalanceItem.vue'
import LeaderboardItem from '@/components/events/LeaderboardItem.vue'
import AvatarItem from '@/components/UI/AvatarItem.vue'

import { usePlayerState } from '@/services/client/usePlayerState'
import { useUser } from '@/composables/useUser.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { type EventRewardCurrency, type Skin } from '@/services/openapi'
import { formatNumberToShortString } from '@/utils/number'
import { BATTLE_EVENT_TEAMS } from '@/constants/events'

import coinIcon from '@/assets/images/temp/big-icons/custom-coin.png'  
import winnerImage from '@/assets/images/temp/instructions/custom-coin-event/3.png'

defineProps<{
  selectedSkin: Skin | null
}>()

const { playerState } = usePlayerState()

const { getUser } = useUser()
const userName = getUser().getName()

const score = computed(() => {
  const teamScore = playerState.value?.battleEventReward?.teamScore ?? 0
  const enemyTeamScore = playerState.value?.battleEventReward?.enemyScore ?? 0
  const totalScore = teamScore + enemyTeamScore
  const teamPercent = Math.round(teamScore / totalScore * 100)
  const enemyTeamPercent = 100 - teamPercent

  return {
    teamScore,
    enemyTeamScore,
    totalScore,
    teamPercent: isNaN(teamPercent) ? 50 : teamPercent,
    enemyTeamPercent: isNaN(enemyTeamPercent) ? 50 : enemyTeamPercent
  }
})

const userData = computed(() => {
  if (!playerState.value) return null
  const rewardInfo = playerState.value.battleEventReward
  return {
    rank: rewardInfo?.playerRank ?? 0,
    score: rewardInfo?.coinsCollected ?? 0,
    balance: rewardInfo?.reward ? getCurrencyRealAmount(rewardInfo.reward.amount, rewardInfo.reward.currency) : 0,
    currency: rewardInfo?.reward?.currency as EventRewardCurrency ?? 'hard',
    league: playerState.value.leagueLevel ?? 1
  }
})
</script>

<template>
  <div
    class="space-y-1 text-white"
    v-if="userData"
  >
    <div
      class="community-battle-banner__box w-full px-[6px] mt-[56px] pt-4 pb-3"
    >
      <div
        class="absolute top-0 -translate-y-[85%] flex flex-col items-center"
        :class="{
          'right-1': score.enemyTeamPercent > score.teamPercent,
          'left-1': score.enemyTeamPercent < score.teamPercent
        }"
      >
        <p class="text-[16px] text-shadow text-shadow_black text-shadow_thin">Winner</p>
        <img :src="winnerImage" alt="winner" class="w-[144px]" />
      </div>

      <div class="flex justify-between items-center gap-x-2 w-full mb-3">
        <div class="flex-1 flex items-center gap-x-1 min-w-0">
          <div class="flex-0">
            <AvatarItem class="flex-0" size="40" :src="BATTLE_EVENT_TEAMS[playerState!.battleEventReward!.teamSkin].avatar" />
          </div>
          <div class="space-y-1 min-w-0">
            <div class="text-[14px] text-shadow text-shadow_black text-shadow_thin truncate px-[2px]">
              {{ BATTLE_EVENT_TEAMS[playerState!.battleEventReward!.teamSkin].name }}
            </div>
            <div class="flex gap-x-5 pl-2">
              <BalanceItem
                icon-name="custom-coin-bg"
                image-class="!w-[23px] !h-[23px]"
              >
                <p class="text-shadow text-shadow_black text-shadow_thin">{{ formatNumberToShortString(score.teamScore) }}</p>
              </BalanceItem>
            </div>
          </div>
        </div>
        <p class="text-[32px] text-shadow text-shadow_black text-shadow_thin">VS</p>
        <div class="flex-1 flex items-center gap-x-1 -scale-x-100 min-w-0">
          <div class="flex-0">
            <AvatarItem class="flex-0" size="40" :src="BATTLE_EVENT_TEAMS[playerState!.battleEventReward!.enemySkin].avatar" />
          </div>
          <div class="space-y-1 min-w-0">
            <div class="text-[14px] text-shadow text-shadow_black text-shadow_thin truncate px-[2px] -scale-x-100">
              {{ BATTLE_EVENT_TEAMS[playerState!.battleEventReward!.enemySkin].name }}
            </div>
            <div class="flex gap-x-5 pl-2">
              <BalanceItem
                icon-name="custom-coin-bg"
                image-class="!w-[23px] !h-[23px]"
              >
                <p class="text-shadow text-shadow_black text-shadow_thin -scale-x-100">{{ formatNumberToShortString(score.enemyTeamScore) }}</p>
              </BalanceItem>
            </div>
          </div>
        </div>
      </div>
      <div class="community-battle-banner__progress-bar flex justify-between items-center gap-x-px w-full">
        <div
          :style="{ '--team-percent': score.teamPercent }"
          class="community-battle-banner__progress-bar__item"
        >
          {{ score.teamPercent }}%
        </div>
        <div
          class="community-battle-banner__progress-bar__item"
        >
          {{ score.enemyTeamPercent }}%
        </div>
      </div>
    </div>
    <p class="text-[14px] text-white font-extrabold text-center">
      {{ score.enemyTeamScore < score.teamScore ? $t('battle_event.reward.winner') : $t('battle_event.reward.loser') }}
    </p>
    <LeaderboardItem
      class="w-full"
      :username="userName"
      :score="formatNumberToShortString(userData.score)"
      :rank-index="userData.rank - 1"
      :balance="userData.balance"
      :league="userData.league"
      :balanceType="userData.currency"
      :scoreTypeImage="coinIcon"
      scoreClass="text-shadow text-shadow_black"
      active
    />
  </div>
</template>

<style lang="scss">
.community-battle-banner {

  &__progress-bar {
    height: 17px;
    padding: 2px;
    background: linear-gradient(360deg, #002564 0%, #00539D 100%);
    border-radius: 3px;

    &__item {
      --team-percent: 0;
      border-radius: 2px;
      height: 100%;

      text-align: center;
      line-height: 13px;
      font-size: 10px;
      color: #fff;
      font-weight: 500;
      overflow: hidden;

      &:first-child {
        flex: 0 0 calc(var(--team-percent) * 1%);
        background: #FFA100;
      }
      
      &:last-child {
        flex: 1 1 auto;
        background: #31D200;
      }
    }
  }
}
</style>
