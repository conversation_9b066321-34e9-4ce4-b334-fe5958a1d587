<script setup lang="ts">
import { computed, ref, watch } from 'vue'

import { usePlayerState } from '@/services/client/usePlayerState'

import eventBanner from '@/assets/images/temp/custom-coin-event/banner.png'
import eventImage from '@/assets/images/temp/custom-coin-event/image.png'
import starImage from '@/assets/images/temp/currency/hard-coin.png'
import coinImage from '@/assets/images/temp/currency/soft-coin.png'
import CountdownTimer from '@/components/UI/CountdownTimer.vue'
import type { ButtonProps } from '../../UI/VButton.vue'
import SkinItem from '../../skins/SkinItem.vue'
import EventBanner from '../EventBanner.vue'

import convertIcon from '@/assets/images/temp/convert-icon.png'
import tonIcon from '@/assets/images/temp/currency/ton.png'
import coinIcon from '@/assets/images/temp/big-icons/custom-coin.png'
import { formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number'
import BalanceItem from '../../UI/BalanceItem.vue'

import boxImage from '@/assets/images/temp/big-icons/starWarsBox.png'
import LeaderboardItem from '@/components/events/LeaderboardItem.vue'
import { useCustomCoinBanners } from '@/composables/useCustomCoinBanners.ts'
import { useCustomCoinEventInfo } from '@/composables/useCustomCoinEventInfo.ts'
import { usePurchase } from '@/composables/usePurchase.ts'
import { useUser } from '@/composables/useUser.ts'
import { SKIN_ID_TO_IMAGE } from '@/constants/skins.ts'
import { usePurchaseSkin, useSkinsList } from '@/services/client/useSkins.ts'
import { useSkinReward } from '@/stores/rewardStore'
import { useToast } from '@/stores/toastStore.ts'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import type { Price } from '@/services/openapi'

const { t } = useI18n()
const { showToast } = useToast()
const router = useRouter()

const COIN_NAME = 'Rocket'
const FIRST_REQUIRED_SKIN_ID = 2012
const SECOND_REQUIRED_SKIN_ID = 2013

const props = defineProps<{
  isOpen: boolean
}>()
const emit = defineEmits(['close', 'start-game'])

const { playerState, refetchPlayerState } = usePlayerState()
const {
  hasMetCustomCoinRequirement,
  hasCustomCoinLeaderboard,
  hasCustomCoinReward,
  userData,
  isCustomCoinEventActive,
  isCustomCoinEventPromoActive,
  customCoinPromoTimeLeftInSeconds,
  customCoinTimeLeftInSeconds,
  customCoinEventBannerType
} = useCustomCoinEventInfo(playerState)
const { isOpenPromoBanner, isOpenRewardBanner, isOpenWelcomeBanner, closeBanner } =
  useCustomCoinBanners()

const { getUser } = useUser()
const userName = getUser().getName()

const close = () => {
  emit('close')
}

const onStartGame = () => {
  emit('start-game')
}

const isLootBoxBannerType = computed(() => {
  return customCoinEventBannerType.value === 'lootBox'
})

const isSkinsBannerType = computed(() => {
  return customCoinEventBannerType.value === 'skins'
})

const eventButtons = computed<Array<ButtonProps>>(() => {
  if (isOpenRewardBanner.value) {
    return [{ text: 'Claim!', type: 'accent', onClick: () => closeBanner() }]
  } else if (hasMetCustomCoinRequirement.value && isCustomCoinEventActive.value) {
    return [{ text: "Let's go!", type: 'success', onClick: onStartGame }]
  } else
    return isLootBoxBannerType.value
      ? [{ text: '5000', type: 'success', image: coinImage, onClick: purchaseBox }]
      : []
})

const skinStore = useSkinReward()
const { skinsList } = useSkinsList()
const { purchaseSkin: purchaseSkinMutation, isPending: isPurchasing } = usePurchaseSkin()

const requiredSkins = computed(() => {
  const requiredSkinsIds = [FIRST_REQUIRED_SKIN_ID, SECOND_REQUIRED_SKIN_ID]
  return skinsList.value
    .filter(s => requiredSkinsIds.includes(s.id))
    .map(s => {
      return {
        id: s.id,
        multiplier: s.multiplier,
        price: s.price!,
        image: SKIN_ID_TO_IMAGE[s.id],
        purchased: s.purchased,
        expensive: s.id === SECOND_REQUIRED_SKIN_ID
      }
    })
})

const playerMultiplier = computed(() => playerState.value!.multiplier ?? 1)

const purchaseSkin = (skinId: number, multiplier: number, price: Price) => {
  const currentMultiplier = playerMultiplier.value
  purchaseSkinMutation(skinId, multiplier, price).then(() => {
    skinStore.showReward([
      {
        skinId: skinId,
        multiplier: currentMultiplier,
        plusMultiplier: multiplier
      }
    ])
  })
  /* .catch(reason => {
      if (reason.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
        router.push({ name: 'shop', query: { scrollTo: 'hard' } })
        props.isOpen ? close() : closeBanner()
      }
    })*/
}

const { purchase } = usePurchase()
const isPendingOpenLootbox = ref(false)

const purchaseBox = () => {
  isPendingOpenLootbox.value = true
  purchase('moonLootBox', 0, 1, { amount: 5000, currency: 'hard' })
    .catch(reason => {
      if (reason.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
        router.push({ name: 'shop', query: { scrollTo: 'hard' } })
        props.isOpen ? close() : closeBanner()
      }
    })
    .finally(() => {
      isPendingOpenLootbox.value = false
    })
}

watch(isOpenWelcomeBanner, () => {
  if (isOpenWelcomeBanner.value && hasCustomCoinLeaderboard.value) {
    closeBanner()
    router.push({
      path: '/custom-coin-event',
      query: { time: customCoinTimeLeftInSeconds.value }
    })
  }
})
</script>

<template>
  <EventBanner
    class="custom-coin-banner w-full h-full"
    :isOpen="props.isOpen || isOpenWelcomeBanner || isOpenRewardBanner || isOpenPromoBanner"
    :buttons="eventButtons"
    :banner="eventBanner"
    :image="eventImage"
    :is-loading="isPurchasing || isPendingOpenLootbox"
    @close="isOpen ? close() : closeBanner()"
    showCloseButton
  >
    <template #details>
      <!-- Event promo timer -->
      <div v-if="customCoinPromoTimeLeftInSeconds > 0" class="custom-coin-banner__timer">
        <h1 class="text-white text-center text-[12px] leading-[17px]">
          Event Starts in:
          <CountdownTimer
            timer-id="customCoinEventPromo"
            class="text-[12px] leading-[16px] text-[#FFFFFF]"
            :total-seconds="customCoinPromoTimeLeftInSeconds"
            @countdown-finished="refetchPlayerState"
          />
        </h1>
      </div>

      <!-- Event timer -->
      <div v-else-if="customCoinTimeLeftInSeconds > 0" class="custom-coin-banner__timer">
        <h1 class="text-white text-center text-[12px] leading-[17px]">
          Event ends at:
          <CountdownTimer
            timer-id="customCoinEventBanner"
            class="text-[12px] leading-[16px] text-[#FFFFFF]"
            :total-seconds="customCoinTimeLeftInSeconds"
            @countdown-finished="refetchPlayerState"
          />
        </h1>
      </div>
    </template>

    <!-- Reward banner -->
    <template v-if="isOpenRewardBanner">
      <div
        class="custom-coin-banner__box flex flex-col justify-center items-center w-full p-3 mb-3"
        :class="{ 'pt-16': hasCustomCoinReward }"
      >
        <LeaderboardItem
          v-if="hasCustomCoinReward"
          class="!absolute w-full"
          :username="userName"
          :score="formatNumberToShortString(userData.score)"
          :scoreTypeImage="coinIcon"
          :rank-index="userData.rank - 1"
          :balance="userData.balance"
          balanceType="hard"
          :league="userData.league"
          scoreClass="text-shadow text-shadow_black"
          active
        />
        <p
          v-else
          class="text-[15px] leading-[20px] text-center whitespace-normal text-shadow text-shadow_black text-white mb-4"
        >
          Event is over, here is your Great catch!
        </p>
        <BalanceItem
          class="mb-2"
          iconName="custom-coin-bg"
          bar-class="custom-coin-banner__bar"
          balance-class="custom-coin-banner__balance text-shadow text-shadow_black text-white"
          image-class="custom-coin-banner__image"
        >
          {{ formatNumberWithSeparator((playerState!.customCoin || userData.score) ?? 0) }}
        </BalanceItem>
        <img :src="convertIcon" class="w-[37px]" alt="convert icon" />
        <BalanceItem
          class="mt-2"
          iconName="hard-coin-bg"
          bar-class="custom-coin-banner__bar"
          balance-class="custom-coin-banner__balance text-shadow text-shadow_black text-white"
          image-class="custom-coin-banner__image !w-[42px] !h-[42px] !-left-[3px]"
        >
          {{
            formatNumberToShortString(
              userData.balance ||
                (playerState!.customCoin ?? 0) * (playerState!.customCoinConvertRate ?? 0)
            )
          }}
          <!-- {{
            formatNumberWithSeparator(
              getCurrencyRealAmount(
                (playerState!.customCoin ?? 0) * (playerState!.customCoinConvertRate ?? 0),
                'ton'
              )
            )
          }} -->
        </BalanceItem>
      </div>
    </template>

    <!-- Promo/Requirement banner for custom coin Skins event -->
    <template
      v-else-if="
        (!hasMetCustomCoinRequirement || isCustomCoinEventPromoActive) && isSkinsBannerType
      "
    >
      <div class="w-full">
        <div class="custom-coin-banner__box text-wrap p-1 mb-2">
          <p
            class="text-white text-[13px] leading-[20px] text-shadow text-shadow_thin text-shadow_black"
          >
            Buy
            <span class="text-shadow_yellow">
              {{ t(`skins.list.${SECOND_REQUIRED_SKIN_ID}.title`) }}</span
            >
            skin to farm
            <span class="text-shadow_yellow">10X coins</span>
            in the event or get
            <span class="text-shadow_yellow">
              {{ t(`skins.list.${FIRST_REQUIRED_SKIN_ID}.title`) }}</span
            >
            skin to farm
            <span class="text-shadow_yellow">1X coins</span>
          </p>
        </div>
        <div class="flex w-full justify-between">
          <div
            v-for="skin in requiredSkins"
            :key="skin.id"
            class="custom-coin-banner__skin"
            :class="{ 'custom-coin-banner__skin_orange': skin.expensive }"
            @click="() => !skin.purchased && purchaseSkin(skin.id, skin.multiplier, skin.price)"
          >
            <div class="custom-coin-banner__skin_info">
              <div v-if="skin.expensive" class="line" />
              <div v-if="skin.expensive" class="line-2" />
              <div v-if="skin.expensive" class="badge">
                <p class="text-[15px] text-white text-shadow text-shadow_thin text-shadow_black">
                  <span class="text-shadow_yellow"> X10</span> Coin boost
                </p>
              </div>
              <p class="text-[15px] text-white text-shadow text-shadow_thin text-shadow_black">
                {{ t(`skins.list.${skin.id}.title`) }}
              </p>
              <div class="h-[90px] w-[90px]">
                <SkinItem :src="skin.image" class="w-full" />
              </div>
            </div>
            <div class="custom-coin-banner__skin_price">
              <p
                v-if="skin.purchased"
                class="text-[16px]"
                :style="{ color: skin.expensive ? '#803f03' : '#1D3161' }"
              >
                Purchased
              </p>

              <div class="flex items-center justify-center gap-1 pb-1" v-else>
                <img class="w-[27px]" :src="starImage" alt="start image" />
                <p class="text-[24px] text-white text-shadow text-shadow_thin text-shadow_black">
                  {{ formatNumberWithSeparator(skin.price?.amount) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- Promo/Requirement banner for custom coin Lootbox event -->
    <template
      v-else-if="
        (!hasMetCustomCoinRequirement || isCustomCoinEventPromoActive) && isLootBoxBannerType
      "
    >
      <div class="w-full pt-1 mb-3">
        <p
          class="custom-coin-banner__description text-[14px] leading-[20px] text-shadow text-shadow_black text-white whitespace-normal"
        >
          Buy Moon Box for 5k coin and take a part in the event with
          <span class="text-shadow_yellow"> 6000 Stars</span>
          prize pool
        </p>
        <div class="custom-coin-banner__box flex items-center gap-x-4 w-full h-[111px] pl-4">
          <div
            class="custom-coin-banner__box-badge text-[14px] text-white text-shadow text-shadow_black"
          >
            Up to <span class="text-shadow_yellow">x50 Coin Boost</span>
          </div>
          <div class="flex-none basis-[69px] -translate-y-1">
            <img :src="boxImage" alt="box" class="w-full" />
          </div>
          <div class="text-left space-y-2 whitespace-normal">
            <p class="text-[24px] text-white text-shadow text-shadow_black">Moon Box</p>
            <p class="text-[13px] leading-[16px] text-[#1E4073]">
              Get Up to x50 Coin Boost for Collecting All Skins from Moon Box!
            </p>
          </div>
        </div>
      </div>
    </template>

    <!-- Balance banner -->
    <template v-else>
      <p class="text-[14px] text-center text-white text-shadow text-shadow_black">
        Catch <span class="text-shadow_yellow">{{ COIN_NAME }} Coin</span> to get TON
      </p>
      <div class="flex items-center justify-center gap-x-2 pt-2 pb-3">
        <img :src="coinIcon" class="w-[37px]" alt="coin icon" />
        <img :src="convertIcon" class="w-[37px]" alt="convert icon" />
        <img :src="tonIcon" class="w-[37px]" alt="ton icon" />
      </div>
      <div
        class="custom-coin-banner__box flex flex-col justify-center items-center gap-y-4 w-full h-[100px] mb-3"
      >
        <p class="text-white text-[20px] text-center text-shadow text-shadow_black">
          Your <span class="text-shadow_yellow">{{ COIN_NAME }} Coin</span> balance
        </p>
        <BalanceItem
          iconName="custom-coin-bg"
          bar-class="custom-coin-banner__bar"
          balance-class="custom-coin-banner__balance text-shadow text-shadow_black text-white"
          image-class="custom-coin-banner__image"
        >
          {{ formatNumberWithSeparator(playerState!.customCoin ?? 0) }}
        </BalanceItem>
      </div>
    </template>
  </EventBanner>
</template>

<style lang="scss">
.custom-coin-banner {
  top: 0;
  --event-background: linear-gradient(360deg, #2969df 0%, #57d8ff 92.65%);

  .event-banner {
    transform: translateY(20px);
    max-width: 360px;
    border: 6px solid #ffd634;

    .close-button {
      --close-btn-background-color: #043870;
    }

    &__banner {
      width: 83%;
      top: 10px;
    }
  }

  &__box {
    position: relative;
    width: 100%;
    background: #3feafe;
    border-radius: 9px;
  }

  &__box-badge {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -50%);
    padding: 0 5px;
    border-radius: 5px;
    background: linear-gradient(180deg, #e228ff 11.36%, #a105e4 127.27%);
    box-shadow: 0 2px 0 0 #7d00a0;
  }

  &__description {
    position: relative;
    top: 10px;
    border-radius: 9px 9px 0 0;
    padding: 15px 15px 24px 15px;
    background: linear-gradient(180deg, #8000f1 0%, #ff58e0 204.12%);
  }

  &__skin {
    --skin-item-background: linear-gradient(360deg, #1eadea 0%, #98e0ff 92.65%);
    --skin-item-price-background-color: #0084e8c7;
    --skin-item-inner-shadow-color-top: #e1f6ff;
    --skin-item-inner-shadow-color-bottom: #006bb5;
    --skin-item-shadow-color: #00000040;

    height: 180px;
    width: 150px;
    position: relative;
    border-radius: 9px;
    background: var(--skin-item-background);
    box-shadow: 0 2px var(--skin-item-shadow-color);

    &:active {
      --skin-item-background: #1eadea;
    }

    &_info {
      position: relative;
      padding: 2px 10px 5px 10px;
      height: 150px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      border-radius: 9px 9px 0 0;
      box-shadow: inset 0 2px var(--skin-item-inner-shadow-color-top);

      .line {
        top: 30px;
        left: 35px;
        position: absolute;
        width: 20px;
        height: 100%;
        transform: rotate(25deg);
        background-color: #ffea4b;
        z-index: 0;
      }

      .line-2 {
        top: 30px;
        left: 55px;
        position: absolute;
        width: 40px;
        height: 100%;
        transform: rotate(25deg);
        z-index: 0;
        background-color: #ffe1007a;
      }

      .badge {
        position: absolute;
        width: 160px;
        height: 20px;
        border-radius: 5px;
        top: 25px;
        background: linear-gradient(360deg, #a105e4 0%, #e228ff 92.65%);
        box-shadow: 0 2px #7d00a0;
        border: none;
      }
    }

    &_price {
      position: absolute;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 30px;
      border-radius: 0 0 9px 9px;
      width: 100%;
      background: var(--skin-item-price-background-color);
      box-shadow: inset 0 -4px var(--skin-item-inner-shadow-color-bottom);
    }

    &_orange {
      --skin-item-background: linear-gradient(360deg, #ffaa00 0%, #fffb00 92.65%);
      --skin-item-price-background-color: #f19204;
      --skin-item-inner-shadow-color-top: #fffacb;
      --skin-item-inner-shadow-color-bottom: #d86800;

      &:active {
        --skin-item-background: #ffaa00;
      }

      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        width: 10px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
        transform: rotate(-26deg);
      }
    }
  }

  &__timer {
    padding: 3px 8px 3px 10px;
    position: relative;
    background: #043870;
    border-radius: 5px;
  }

  &__bar {
    height: 32px;
    padding-left: 28px;

    &::after {
      background: #004d889c;
    }
  }

  &__balance {
    font-size: 24px;
  }

  &__image {
    width: 38px;
    height: 38px;
  }
}
</style>
