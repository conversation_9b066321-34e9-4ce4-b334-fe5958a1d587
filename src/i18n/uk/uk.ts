export const uk = {
  actions: {
    allowAccess: 'Дозволити доступ',
    back: 'Назад',
    cancelTransaction: 'Скасувати транзакцію',
    claim: 'Отримати',
    collect: 'Забрати',
    connectWallet: 'Підключити гаманець',
    continue: 'Продовжити',
    create: 'Створити',
    disconnect: "Від'єднати",
    getReward: 'Отримати винагороду',
    goToHome: 'Перейти на головну',
    goToShop: 'Перейти до магазину',
    goToSkins: 'Перейти до скінів',
    inviteFriend: 'Запросити друга',
    leave: 'Вийти',
    makeTransaction: 'Здійснити транзакцію',
    play: 'Грати',
    playAgain: 'Грати знову',
    select: 'Вибрати',
    startFarming: 'Фармити',
    subscribe: 'Підписатися',
    tapToCollect: 'Отримати',
    tapToContinue: 'Натисніть\nщоб продовжити',
    tapToJump: 'Торкніться, щоб стрибнути',
    tapToOpen: 'Натисніть, щоб відкрити',
    tapToPlay: 'Натисніть щоб зіграти',
    tapToShoot: 'Натисніть, щоб стріляти',
    tapToSkip: 'Натисніть, щоб пропустити',
    withdraw: 'Зняти',
    hold: 'Утримуйте для автообертання',
    spin: 'Крутити',
    stop: 'Стоп',
    openChat: 'Відкрити чат',
    startEvent: 'Почати подію',
    join: 'Приєднатися',
    got: 'Зрозуміло!',
    open_telegram: 'Відкрити Telegram'
  },
  search: 'Пошук',
  boosters: {
    stackableAimbot: {
      description: 'Збільшує кількість монет, зібраних за сесію.',
      fullName: 'AIM Бот',
      name: 'AIM Бот'
    },
    stackableJumper: {
      description: 'Збільшує кількість монет, зібраних за сесію.',
      fullName: 'Пружина',
      name: 'Пружина'
    },
    stackableMagneticField: {
      description: 'Збільшує кількість монет, зібраних за сесію.',
      fullName: 'Магнітне поле',
      name: 'Магніт'
    },
    title: 'Бустери',
    endlessRun: 'Endless Run',
    rewards: 'Винагороди, які ви можете отримати:',
    select: 'Виберіть бустери:'
  },
  warning: 'Увага',
  mainMenu: 'Головне меню',
  pausedOnBack: 'Ви впевнені, що хочете покинути гру?',
  gameover: 'Кінець гри',
  totalScore: 'Загальний рахунок:',
  currentScore: 'Поточний рахунок:',
  allowGyroscope: 'Дозволити\nгіроскоп',
  free: 'Безплатно',
  selected: 'Вибрано',
  hotRecord: 'Hot Record',
  joinUs: 'Долучайся!',
  termsOfUse: 'Умови Використання',
  faq: 'Часті питання',
  swipeScreen: 'Керуйте пальцем\nпо екрану',
  controlMethod: {
    gyroscope: 'Гіроскоп',
    swipe: 'Свайп',
    selectMsg: 'Оберіть зручний спосіб керування',
    selected: 'Вибрано',
    select: 'Вибрати'
  },
  features: {
    customCoinEvent: 'Подія зі скінами',
    dailyReward: 'Щоденні винагороди',
    dynamicCoins_1: 'Чарівна веселка',
    dynamicCoins_2: 'Глибоке занурення',
    farming: 'Фармінг',
    hotRecordEvent: 'Гарячий рекорд',
    lives: 'Життя',
    onePercentEvent: 'Клуб 1%',
    tonMiningEvent: 'Тон Майнінг',
    withdraw: 'Зняття',
    iceFragment: 'Льодяний фрагмент'
  },
  lootboxes: {
    rainbowLootBox: 'Rainbow Box',
    luckyLootBox: 'Lucky Box',
    cryptoLootBox: 'Crypto Box',
    moonLootBox: 'Moon Box',
    easterLootBox: 'Easter Box',
    minecraftLootBox: 'Craft Box',
    bananaLootBox: 'Banana Box',
    starWarsLootBox: 'Star Wars Box',
    infinityLootBox: 'Infinity Box',
    kungFuLootBox: 'Kung Fu Box',
    spongeLootBox: 'Sponge Box'
  },
  skins: {
    title: 'Скіни',
    description: 'Множник скінів — купляй більше для підвищення множника',
    yourSkins: 'Твої скіни: {amount}/{total}',
    newSkin: 'Ви отримали новий скін!',
    list: {
      '-1': {
        title: 'Uni',
        description: 'Uni великий мрійник — вірить у неможливе і надихає всіх навколо!'
      },
      0: {
        title: 'Dizzy Uni',
        description: 'Dizzy Uni крутиться в хаосі, готовий до шаленої їзди!'
      },
      1: {
        title: 'Emo-Uni',
        description: 'Emo Uni не посміхається, але почуття — найголовніше.'
      },
      2: {
        title: 'Uni Plumber',
        description: 'Uni Plumber — найкращі сантехнічні послуги з дрібкою магічних прокрутів!'
      },
      3: {
        title: 'Uni Kitty',
        description: 'Uni Kitty це мур-фектне поєднання веселощів та пригод!'
      },
      4: {
        title: 'Uni Ninja',
        description: 'Тихий і швидкий Uni Ninja завжди на крок попереду.'
      },
      5: {
        title: 'McUni',
        description: 'Спочатку картопля фрі, а потім запитання — McUni зайнятий їжею!'
      },
      6: {
        title: 'Uni Potter',
        description: 'З кожним стрибком Uni Potter, світ наповнюється чарами!'
      },
      7: {
        title: 'Devil Uni',
        description: 'Готові до проблем? Devil Uni зараз допоможе!'
      },
      8: {
        title: 'Super Uni',
        description: 'Super Uni тут, щоб врятувати ситуацію!'
      },
      9: {
        title: 'Resistance Uni',
        description: 'Приєднуйся до Цифрового Опору разом з Resistance Uni!'
      },
      10: {
        title: 'Uni Diver',
        description: 'Uni Diver буквально скрізь залишає сліди бульбашок!'
      },
      11: {
        title: 'Chicken Uni',
        description: "Chicken Uni — пір'яний друг, що завжди несе сюрпризи, як курка яйця!"
      },
      12: {
        title: 'Capy Uni',
        description: 'Capy Uni каже, що тепла ванна — найкраще місце для гри.'
      },
      13: {
        title: 'Uni Poop',
        description: 'Смердючий, але гордий, Uni Poop не боїться бути собою!'
      },
      14: {
        title: 'Pepe Uni',
        description: 'Пригоди кращі з класичним луком Pepe Uni!'
      },
      1000: {
        title: 'Uni Inu',
        description: 'Гавкайте, сяйте, повторюйте по колу — Uni Inu неможливо зупинити!'
      },
      1001: {
        title: 'Uni Trump',
        description: '«Make Adventures Great Again» — ось девіз Uni Trump!'
      },
      1002: {
        title: 'Chainsaw Uni',
        description: 'Chainsaw Uni перетворює перешкоди на тирсу. Бжжж!'
      },
      1003: {
        title: 'SWAG Uni',
        description: 'SWAG Uni — золото, блиск і привабливість в кожному русі!'
      },
      1004: {
        title: 'Uni Fremen',
        description: 'Стрибай за свободу, розбий кожного монстра!'
      },
      1005: {
        title: 'Grump Uni',
        description: 'Стрибок, падіння, зітхання... Найсумніший збирач у Всесвіті'
      },
      1006: {
        title: 'Peck Uni',
        description:
          'Стрибає швидко, падає сильно, завжди скаржиться. Велика драма, маленька пташка!'
      },
      1007: {
        title: 'Uni Duo',
        description: 'Кумедна сова вчить тебе стрибати. Не пропускай її уроки.'
      },
      1008: {
        title: 'Spring Uni',
        description: 'Свіжий, веселий і любить зелені квитки.'
      },
      1009: {
        title: 'Uni Magic',
        description: 'Молодий Юні з деякою магією, але не дуже щасливий.'
      },
      1010: {
        title: 'Uni Luck',
        description: 'Багатий і щасливий, старий Юні, який ловить у 10 разів більше монет!'
      },
      2000: {
        title: 'Chill Uni',
        description: 'Завжди спокійний, завжди стильний. Його холодний розум - запорука перемоги!'
      },
      2001: {
        title: 'Mystic Uni',
        description: 'Майстер магії та загадок. Один рух – і реальність змінюється!'
      },
      2002: {
        title: 'Uni Beat',
        description: 'Він не просто слухає музику - він керує нею!'
      },
      2003: {
        title: 'Uni Relic',
        description: 'Древній дух, що повернувся. Його сила непідвладна часу!'
      },
      2004: {
        title: 'Uni Reaper',
        description: 'Таємничий мисливець за душами, що приходить найтемнішими ночами.'
      },
      2005: {
        title: 'Uni Inferno',
        description: 'Справжній демон вогню, який спалює все на своєму шляху.'
      },
      2006: {
        title: 'Uni Bera',
        description: 'Симпатиче ведмежа з вайбами Ooga Booga'
      },
      2007: {
        title: 'Ponke Uni',
        description: 'Божевільний, дегенерат, завжди жадає квитків'
      },
      2008: {
        title: 'Uni Bull',
        description: 'Тільки вгору, як на кожному крипто-ринку'
      },
      2009: {
        title: 'Uni Doge',
        description: 'Гав-гав, качай і збирай'
      },
      2010: {
        title: 'Uni Popcat',
        description: 'Їсть TON з широко відкритим ротом'
      },
      2011: {
        title: 'Uni Pengu',
        description: "М'які вібрації, веселі рухи, великі перемоги"
      },
      2012: {
        title: 'Uni Ronald',
        description: 'Стрибає за картоплею фрі та колою, але збирає квитки.'
      },
      2013: {
        title: 'Uni Art',
        description: 'Посміхається, коли Експлойтери кричать, і забирають ваші квитки.'
      },
      2014: {
        title: 'Anon Uni',
        description: 'Обличчя залишається прихованим, лояльність - ніколи!'
      },
      2015: {
        title: 'Uni Shroom',
        description: 'Червона кепка, Airdrop попереду!'
      },
      2016: {
        title: 'Uni Princess',
        description: 'Стрибки такі елегантні, як у королеви!'
      },
      2017: {
        title: 'Uni Cloud',
        description: "М'який, стрибає високо, ловіть ТОН з неба!"
      },
      2018: {
        title: 'Uni Fairy',
        description: 'Рідка сила, злива квитків!'
      },
      2019: {
        title: 'Uni Mixture',
        description: 'Чарівний шарм, чекає на Airdrop!'
      },
      2020: {
        title: 'Uni Genie',
        description: 'Бажання збуваються - увесь цей ТОН для вас!'
      },
      2021: {
        title: 'Uni Rubik',
        description: 'Крутись і вертись, TON заробляй!'
      },
      2022: {
        title: 'Uni Gift',
        description: 'Розпаковуйте веселощі та подарунки для всіх!'
      },
      2023: {
        title: 'Uni Rich',
        description: 'Золотий зверху до низу, фармить Airdrop нон-стоп!'
      },
      2024: {
        title: 'Uni Neko',
        description: 'Лапа на удачу ТОН притягує!'
      },
      2025: {
        title: 'Uni Rabbit',
        description: 'Швидко і безкоштовно ловить квитки!'
      },
      2026: {
        title: 'Uni Dice',
        description: 'Стрибай і крутись, удачі до перемоги!'
      },
      2027: {
        title: 'Uni Radio',
        description: 'Сигнал сильний, допоки ловить ТОН цілий день!'
      },
      2028: {
        title: 'Uni NightSky',
        description: 'Ніч така ясна, а ТОН близько!'
      },
      2029: {
        title: 'Uni Lumpy',
        description: 'Дозвольте Пупирці вести вас до епічних нагород!'
      },
      2030: {
        title: 'Uni Rocket',
        description: 'Летимо на Місяць, підкачуючи UNICOIN!'
      },
      2031: {
        title: 'Uni Luna',
        description: 'Магічний Місячний кіт перетворює стрибки на квитки.'
      },
      2032: {
        title: 'Astro Uni',
        description: 'Стрибає так високо, що не може дихати без костюма.'
      },
      2033: {
        title: 'Uni Basket',
        description: 'Наповнений бонусами і готовий до стрибка'
      },
      2034: {
        title: 'Chicken Uni',
        description: 'Літати не вміє, але стрибає як чемпіон!'
      },
      2035: {
        title: 'Uni Bunny',
        description: 'Гоп-гоп на вершину!'
      },
      2036: {
        title: 'Uni Carrot',
        description: 'Солодкий, помаранчевий, нестримний.'
      },
      2037: {
        title: 'Uni Egg',
        description: 'А що всередині? Тільки стрибки покажуть!'
      },
      2038: {
        title: 'Uni Tulip',
        description: 'Квітка зі стрибкоподібною силою.'
      },
      2039: {
        title: 'Uni Citizen',
        description: 'Базовий скін, все одно виграш!'
      },
      2040: {
        title: 'Uni TNT',
        description: "Стрибай, збирай і Бум з'єднуй!"
      },
      2041: {
        title: 'Uni Zombie',
        description: 'З могили в небо він злітає високо!'
      },
      2042: {
        title: 'Uni Diamond',
        description: 'Броня встановлена, найкращий стрибок!'
      },
      2043: {
        title: 'Uni Steve',
        description: 'Від куба до хмари Стів стрибає з гордістю!'
      },
      2044: {
        title: 'Uni Ender',
        description: 'Моторошний і крутий, порушує всі правила!'
      },
      2045: {
        title: 'Uni Creeper',
        description: 'Один великий вибух, і кімната очищається!'
      },
      2046: {
        title: 'Uni Agnes',
        description: 'З великими очима, з радістю, ТОН вже близько!'
      },
      2047: {
        title: 'Uni Edith',
        description: 'Сміливі та спритні, стрибки чіпкі!'
      },
      2048: {
        title: 'Uni Balthazar',
        description: 'Танцюй і кружляй, стрибай до перемоги!'
      },
      2049: {
        title: 'Uni Kevin',
        description: 'Жовта і дика, стрибуча дитина!'
      },
      2050: {
        title: 'Uni Evil',
        description: 'Фіолетовий звір, свято квитків!'
      },
      2051: {
        title: 'Uni Gru',
        description: 'Підступний план, ТОН для клану!'
      },
      2052: {
        title: 'Uni Vector',
        description: 'План Вектора? Лови, якщо зможеш!'
      },
      2053: {
        title: 'Uni Waider',
        description: 'Стрибай і пірнай, відчуй себе живим!'
      },
      2054: {
        title: 'Jedi Uni',
        description: 'Сила в повітрі, квитки скрізь!'
      },
      2055: {
        title: 'Sith Uni',
        description: 'Тіні кличуть, квитки падають!'
      },
      2056: {
        title: 'Uni Storm',
        description: 'Стрілянина по монстрах – квитки на прицілі!'
      },
      2057: {
        title: 'Grogu Uni',
        description: 'Маленький і швидкий, чарівний малюк!'
      },
      2058: {
        title: 'Wookiee Uni',
        description: 'Великий і гучний, стрибає гордо!'
      },
      2059: {
        title: 'Mando Uni',
        description: 'Ловити квитки - такий шлях!'
      },
      2060: {
        title: 'Captain Uni',
        description: 'Leap with might, win the fight!'
      },
      2061: {
        title: 'Hulk Uni',
        description: "Big green hop, can't stop!"
      },
      2062: {
        title: 'Iron Uni',
        description: 'Armor on, jumping strong!'
      },
      2063: {
        title: 'Uni Loki',
        description: 'Laugh and flee, ticket spree!'
      },
      2064: {
        title: 'Spider Uni',
        description: 'Web of luck, tickets stuck!'
      },
      2065: {
        title: 'Thanos Uni',
        description: 'Power punch, big ticket bunch!'
      },
      2066: {
        title: 'Thor Uni',
        description: 'Thunder power, ticket shower!'
      },
      2067: {
        title: 'Uni Po',
        description: 'Hero of snacks, fights with whacks!'
      },
      2068: {
        title: 'Shifu Uni',
        description: 'Calm and quick, master trick!'
      },
      2069: {
        title: 'Mr. Ping Uni',
        description: 'Bowl jump fun, tickets run!'
      },
      2070: {
        title: 'Kai Uni',
        description: 'Horned and mean, TON machine!'
      },
      2071: {
        title: 'Tai Lung Uni',
        description: 'Cold and bold, TON unfolds!'
      },
      2072: {
        title: 'Tigress Uni',
        description: 'Eyes that burn, watch her turn!'
      },
      2073: {
        title: 'Oogway Uni',
        description: 'Wise and slow, TON will grow!'
      },
      2074: {
        title: 'Gary Uni',
        description: 'Slow but sure, TONs secure!'
      },
      2075: {
        title: 'Krabs Uni',
        description: 'Shell so slick, TON comes quick!'
      },
      2076: {
        title: 'Patrick Uni',
        description: 'Starry spin, tickets win!'
      },
      2077: {
        title: 'Sponge Uni',
        description: 'Jelly jump, tickets pump!'
      },
      2078: {
        title: 'Plankton Uni',
        description: 'Secret plan, tickets in the pan!'
      },
      2079: {
        title: 'Sandy Uni',
        description: 'Jumps with brains, TON in chains!'
      },
      2080: {
        title: 'Squidward Uni',
        description: 'Grumpy dash, still gets cash!'
      },
      3000: {
        title: 'Bored Uni',
        description: 'Every jump is a gamble, every fall is a lesson.'
      }
    },
    requirenments: {
      inviteFriend: 'Запроси більше друзів: {amount}',
      wallet: "Під'єднай гаманець",
      transaction: 'Зроби першу транзакцію',
      box: '{boxType} Бокс',
      dailyReward: 'Днів, щоб отримати: {days}',
      inProgress: 'В процесі...'
    }
  },
  achievements: {
    completed: 'Завершено',
    description: 'Отримайте бонус до свого множника!',
    list: {
      1: {
        1: {
          description: 'Запросити 1 друга',
          name: 'Щасливі разом'
        },
        2: {
          description: 'Запросити 10 друзів',
          name: 'Місцевий бос'
        },
        3: {
          description: 'Запросіть 100 друзів',
          name: 'Реферальний вихваляка'
        },
        4: {
          description: 'Запросити 500 друзів',
          name: 'Інфлюенсер'
        },
        5: {
          description: 'Запросити 1000 друзів',
          name: 'Лідер Єдинорогів'
        }
      },
      2: {
        1: {
          description: 'Відродитись 1 раз',
          name: 'Упс...'
        },
        2: {
          description: 'Відродитися 10 разів',
          name: 'Більше, ніж кішка.'
        },
        3: {
          description: 'Відродитися 100 разів',
          name: 'Я повернуся'
        },
        4: {
          description: 'Відродитися 1 000 разів',
          name: 'Не сьогодні'
        },
        5: {
          description: 'Відродитися 10 000 разів',
          name: 'Фенікс'
        }
      },
      3: {
        1: {
          description: 'Рекорд:\n10 000',
          name: 'Нова залежність'
        },
        2: {
          description: 'Рекорд:\n100 000',
          name: 'Студент-стрибунець'
        },
        3: {
          description: 'Рекорд:\n300 000',
          name: 'Вгору! Вгору! Вгору!'
        },
        4: {
          description: 'Рекорд:\n500 000',
          name: 'Майстер стрибків'
        },
        5: {
          description: 'Рекорд:\n1 000 000',
          name: 'До Місяця'
        }
      },
      4: {
        1: {
          description: 'Всього витрачено монет:\n10 000',
          name: 'Бідний студент'
        },
        2: {
          description: 'Всього витрачено монет:\n100 000',
          name: 'Розумний споживач'
        },
        3: {
          description: 'Всього витрачено монет:\n500 000',
          name: 'Скрудж МакДак'
        },
        4: {
          description: 'Всього витрачено монет:\n1 000 000',
          name: 'Золотий мільйонер'
        },
        5: {
          description: 'Всього витрачено монет:\n2 000 000',
          name: 'Золота картка'
        }
      },
      5: {
        1: {
          description: 'Стрибнути на 10 монстрів',
          name: 'Боньк Боньк'
        },
        2: {
          description: 'Стрибнути на 30 монстрів',
          name: 'Ой, розчавило'
        },
        3: {
          description: 'Стрибнути на 50 монстрів',
          name: 'Стрибкопокаліпсис'
        },
        4: {
          description: 'Стрибнути на 100 монстрів',
          name: 'Бадьора помста'
        },
        5: {
          description: 'Стрибнути на 300 монстрів',
          name: 'Розтрощені закуски'
        }
      },
      6: {
        1: {
          description: 'Застрелити 10 монстрів',
          name: 'Творець болю'
        },
        2: {
          description: 'Застрелити 30 монстрів',
          name: 'Хірург з дробовиком'
        },
        3: {
          description: 'Застрелити 50 монстрів',
          name: 'Рукоділля'
        },
        4: {
          description: 'Застрелити 100 монстрів',
          name: 'Бий і біжи'
        },
        5: {
          description: 'Застрелити 300 монстрів',
          name: 'Хіт-парад'
        }
      },
      7: {
        1: {
          description: 'Використати пропелер/джетпак 3 рази за гру',
          name: 'Долетіти до Місяця'
        },
        2: {
          description: 'Використати пропелер/джетпак 5 разів за гру',
          name: 'Занадто багато соку'
        },
        3: {
          description: 'Використати пропелер/джетпак 10 разів за гру',
          name: 'Піднесений понад віру'
        }
      },
      8: {
        1: {
          description: 'Убити 100 монстрів',
          name: 'Перша кров'
        },
        2: {
          description: 'Вбити 500 монстрів',
          name: 'Лютий вбивця'
        },
        3: {
          description: 'Вбити 1 000 монстрів',
          name: 'Каратель'
        },
        4: {
          description: 'Вбити 5 000 монстрів',
          name: 'Термінатор'
        },
        5: {
          description: 'Вбити 10 000 монстрів',
          name: 'Режим звіра ввімкнено'
        }
      },
      9: {
        1: {
          description: 'Зламати 10 крихких платформ у грі',
          name: 'Руйнівник платформ'
        },
        2: {
          description: 'Зламати 50 крихких платформ у грі',
          name: 'Товстун'
        },
        3: {
          description: 'Зламати 100 крихких платформ у грі',
          name: 'Занадто рано приземлений'
        },
        4: {
          description: 'Зламати 300 крихких платформ у грі',
          name: 'Я знову це зламав'
        },
        5: {
          description: 'Зламати 500 крихких платформ у грі',
          name: 'Падаюча зірка'
        }
      },
      10: {
        1: {
          description: 'Пролетіти з джетпаком 10 разів',
          name: 'Прогрів двигуна'
        },
        2: {
          description: 'Пролетіти з джетпаком 50 разів',
          name: 'Пілот-Ас'
        },
        3: {
          description: 'Пролетіти з джетпаком 200 разів',
          name: 'Злітаємо!'
        },
        4: {
          description: 'Пролетіти з джетпаком 1 000 разів',
          name: 'Наркоман з джетпаком'
        },
        5: {
          description: 'Пролетіти з джетпаком 2 500 разів',
          name: 'Залежний від висоти'
        }
      },
      11: {
        1: {
          description: 'Пролетіти з пропелером 10 разів',
          name: 'Фааантастична гра'
        },
        2: {
          description: 'Пролетіти з пропелером 50 разів',
          name: 'Оператор дрону'
        },
        3: {
          description: 'Пролетіти з пропелером 200 разів',
          name: 'Майстер вітроенергетики'
        },
        4: {
          description: 'Пролетіти з пропелером 1 000 разів',
          name: 'Король повітряних потоків'
        },
        5: {
          description: 'Пролетіти з пропелером 2 500 разів',
          name: 'Пропеллер Про 2500'
        }
      },
      12: {
        1: {
          description: 'Розблокувати 1 скін',
          name: 'Новий гардероб'
        },
        2: {
          description: 'Розблокувати 5 скінів',
          name: "Скажіть 'так' костюму"
        },
        3: {
          description: 'Розблокувати 10 скінів',
          name: 'На стилі'
        },
        4: {
          description: 'Розблокувати 20 скінів',
          name: 'Елітний колекціонер'
        },
        5: {
          description: 'Розблокувати 40 скінів',
          name: 'Бог стилю'
        }
      },
      13: {
        1: {
          description: 'Загальний рахунок в усіх іграх:\n10 000',
          name: 'Слабкий рахунок'
        },
        2: {
          description: 'Загальний рахунок в усіх іграх:\n100 000',
          name: 'Збери їх усіх!'
        },
        3: {
          description: 'Загальний рахунок в усіх іграх:\n1 000 000',
          name: 'Сон переоцінюють'
        },
        4: {
          description: 'Загальний рахунок в усіх іграх:\n10 000 000',
          name: 'Активовано режим грінду'
        },
        5: {
          description: 'Загальний рахунок в усіх іграх:\n100 000 000',
          name: 'Forbes 100 до 100'
        }
      },
      14: {
        1: {
          description: 'Стрибнути 1 000 разів',
          name: 'Стрибки довкола'
        },
        2: {
          description: 'Стрибнути 10 000 разів',
          name: 'Чемпіон зі стрибків'
        },
        3: {
          description: 'Стрибнути 100 000 разів',
          name: 'Дотягнутись до хмар'
        },
        4: {
          description: 'Стрибнути 250 000 разів',
          name: 'Першокласний стрибун'
        },
        5: {
          description: 'Стрибнути 1 000 000 разів',
          name: 'Вище за хмари'
        }
      },
      15: {
        1: {
          description: 'Стрибнути 10 разів на батуті',
          name: 'Новачок в стрибках'
        },
        2: {
          description: 'Стрибнути 100 разів на батуті',
          name: 'Вгору, вниз, і ще раз'
        },
        3: {
          description: 'Стрибнути 1 000 разів на батуті',
          name: 'Стрибок у халепу'
        },
        4: {
          description: 'Стрибнути 10 000 разів на батуті',
          name: 'Коліна досі підстрибують'
        },
        5: {
          description: 'Стрибнути 100 000 разів на батуті',
          name: 'Майстер батута'
        }
      },
      16: {
        1: {
          description: 'Стрибнути 10 разів на пружині',
          name: 'Новачок в пружинах'
        },
        2: {
          description: 'Стрибнути 100 разів на пружині',
          name: 'Нудота від стрибків'
        },
        3: {
          description: 'Стрибнути 1 000 разів на пружині',
          name: 'Колінам торба'
        },
        4: {
          description: 'Стрибнути 10 000 разів на пружині',
          name: 'Я так більше не можу.'
        },
        5: {
          description: 'Стрибнути 100 000 разів на пружині',
          name: 'Кому потрібні батути?'
        }
      }
    },
    tapToClaim: 'Отримати',
    title: 'Досягнення'
  },
  soon: 'Скоро',
  portraitOrientationRequired: 'Використовуйте портретну орієнтацію для кращого досвіду',
  loading: 'Завантаження',
  settings: {
    title: 'Налаштування',
    music: 'Музика',
    sound: 'Звук',
    haptic: 'Вібрація',
    language: 'Мова',
    support: 'Підтримка'
  },
  farming: {
    start: 'Почати фармінг',
    farming: 'Фармінг'
  },
  friends: {
    title: 'Друзі',
    inviteFriends: 'Запросити друзів',
    description: 'Отримай бонус за кожного друга!',
    invitedCount: 'Твої реферали: {count}',
    freeInviteDesc: 'За друга',
    premiumInviteDesc: 'За друга з Premium',
    emptyRefs: 'Запрошуй друзів, щоб отримати\nсоковиті нагороди та грати разом'
  },
  earn: {
    title: 'Місії',
    name: 'Завдання',
    description: 'Виконуй завдання та отримуй винагороди',
    timeToVerify: 'Час перевірки завдання — 1 год',
    startTasks: 'Початкові завдання',
    dailyTasks: 'Щоденні завдання',
    completedMissions: 'Завершені завдання',
    tasksCompleted: 'Місій виконано:',
    missions: {
      title: 'Місії',
      partners: 'Партнери',
      invite_5_friend: {
        name: 'Запроси 5 друзів',
        action: 'Запросити'
      },
      invite_3_friend: {
        name: 'Запроси 3 друзів',
        action: 'Запросити'
      },
      invite_1_friend: {
        name: 'Запроси 1 друга',
        action: 'Запросити'
      },
      connect_wallet: {
        name: "Під'єднати TON гаманець",
        action: "Під'єднати"
      },
      first_transaction: {
        name: 'Зроби першу TON транзакцію',
        action: 'Зробити транзакцію'
      },
      subscribe_main_channel: {
        name: 'Підпишись на основний канал',
        action: 'Підписатися'
      },
      use_booster: {
        name: 'Спіймай бустерів {goal}',
        action: 'Грати'
      },
      jump_to_score: {
        name: 'Набери рахунок за гру: {goal}',
        action: 'Грати'
      },
      kill_monster: {
        name: 'Вбий {goal} монстрів',
        action: 'Грати'
      },
      invite_ref: {
        name: 'Запроси друзів: {goal}',
        action: 'Запросити'
      },
      play_game: {
        name: 'Зіграй ігор: {goal}',
        action: 'Грати'
      },
      catch_ticket: {
        name: 'Піймай {goal} Квитків',
        action: 'Грати'
      },
      daily_total_jump: {
        name: 'Отримай {goal} за всі ігри',
        action: 'Грати'
      },
      use_aimbot_booster: {
        name: 'Використай {goal} AIM Бот',
        action: 'Грати'
      },
      use_jumper_booster: {
        name: 'Використай {goal} Пружину',
        action: 'Грати'
      },
      use_magnet_booster: {
        name: 'Використай {goal} Магніт',
        action: 'Грати'
      },
      unlock_league: {
        name: 'Розблокуй {goal} Лігу',
        action: 'Грати'
      },
      buy_skin: {
        name: 'Отримай скін',
        action: 'До скінів'
      },
      use_revive: {
        name: 'Використай {goal} відроджень',
        action: 'Грати'
      },
      subscribe_x: {
        name: 'Підпишись на наш X',
        action: 'Підписатися'
      },
      subscribe_community_chat: {
        name: 'Доєднайся до Uni Jump спільноти',
        action: 'Доєднатися'
      },
      add_to_home_screen: {
        name: 'Додай Uni Jump на домашню сторінку',
        action: 'Додати'
      },
      purchase_in_shop_for_stars: {
        name: 'Купи на 100 зірок в магазині',
        action: 'Купити'
      },
      purchase_skin_for_stars: {
        name: 'Купи будь-який скін за зірки',
        action: 'Купити'
      },
      boost_telegram_channel: {
        name: 'Забусти наш Телеграм канал',
        action: 'Забустити'
      },
      go_to_miniapp_9: {
        name: 'Play Match-3, watch ads, and earn USDt!',
        action: 'Play MatchMoney'
      },
      go_to_miniapp_10: {
        name: 'Boinker: spin the slot and collect Artifacts',
        action: 'Join Boinkers'
      },
      go_to_miniapp_15: {
        name: 'Play Miner to win $300',
        action: 'Play Miner'
      },
      go_to_miniapp_19: {
        name: 'Open chests — get USDT',
        action: 'Join Digger game'
      },
      go_to_miniapp_22: {
        name: 'Start merging cars today!',
        action: 'Join DRFT Party'
      },
      go_to_miniapp_23: {
        name: 'You Play - We Really Pay!',
        action: 'Join BrickWalls'
      },
      go_to_miniapp_24: {
        name: 'Play Outmine',
        action: 'Join Outmine'
      },
      go_to_miniapp_25: {
        name: 'Breed ducks to earn $EGG tokens!',
        action: 'Play DuckyGram'
      },
      go_to_miniapp_26: {
        name: 'Launch Symptomify',
        action: 'Play Symptomify'
      },
      go_to_miniapp_27: {
        name: 'Play WORK DOGS',
        action: 'Join WORK DOGS'
      },
      go_to_miniapp_28: {
        name: 'Tap & Earn $HASH',
        action: 'Play HashCats'
      },
      go_to_miniapp_29: {
        name: 'Check in, open boxes & get $RICH',
        action: 'Join RichDogs'
      },
      go_to_miniapp_30: {
        name: 'Play Simple Tap',
        action: 'Join Simple Tap'
      },
      go_to_miniapp_31: {
        name: 'Play Pookie & get TON boxes!',
        action: 'Play Pookie Cheese'
      },
      go_to_miniapp_32: {
        name: 'Mine TON in TonTower',
        action: 'Play TonTower'
      },
      go_to_miniapp_33: {
        name: 'Play Fasqon & eran FSQN tokens',
        action: 'Join Fasqon'
      },
      go_to_miniapp_34: {
        name: 'Play Capybara MEME',
        action: 'Join Capybara MEME'
      },
      go_to_miniapp_35: {
        name: 'Launch 🚀 and withdraw $$$',
        action: 'Join Space Adventure'
      },
      go_to_miniapp_36: {
        name: 'Get Free Stars🌟',
        action: 'Join StarCoin'
      },
      go_to_miniapp_37: {
        name: 'Join Daily Combo Updates',
        action: 'Join Daily Combo'
      },
      go_to_miniapp_38: {
        name: 'Play JustFab',
        action: 'Join JustFab'
      },
      go_to_miniapp_39: {
        name: 'Join TAPX',
        action: 'Join TAPX'
      },
      go_to_miniapp_40: {
        name: 'Play Pixiland & Claim $wPIXI now!',
        action: 'Join Pixiland'
      },
      go_to_miniapp_41: {
        name: 'Play Biz Tycoon Now',
        action: 'Join Biz Tycoon  '
      },
      go_to_miniapp_42: {
        name: 'Join Appss',
        action: 'Join Appss'
      },
      go_to_miniapp_43: {
        name: 'Join Puparty and spin 1 slot',
        action: 'Join Puparty'
      },
      go_to_miniapp_44: {
        name: 'Join Gumart',
        action: 'Join Gumart'
      }
    }
  },
  shop: {
    title: 'Магазин',
    friendsDescription: 'Купуй віртуального друга та \nотримай бонуси як за реального!',
    bestDeal: 'Вигода',
    new: 'НОВЕ',
    free: 'БЕЗПЛАТНО',
    boxDescription: 'Ви можете отримати одну з цих нагород',
    nextFree: 'Наступний безкоштовний після'
  },
  airdrop: {
    button: 'Дроп',
    title: 'Airdrop',
    tasks: {
      info: 'Виконуй завдання, щоб взяти участь в Airdrop!',
      connectWallet: 'Підключіть свій гаманець TON',
      transaction: 'Зробіть TON транзакцію'
    },
    ticketsBanner: 'Квитки — єдиний спосіб отримати Airdrop, більше квитків - більше винагород',
    instructionSteps: {
      1: '1. Підключіть свій гаманець TON',
      2: '2. Давайте зробимо вашу першу транзакцію, щоб довести, що ви готові до Airdrop! Це коштує лише 0,5 ТОН.'
    },
    instructionText:
      'Ви можете отримати TON на свій гаманець на будь-якій з наступних бірж:\n      Binance, Bybit, KuCoin, OKX або Bitget.',
    comingSoon: "AIRDROP — НЕЗАБАРОМ З'ЯВИТЬСЯ!"
  },
  wallet: {
    title: 'Гаманець',
    assets: 'Активи',
    connectWallet: 'Підключіть гаманець для виведення коштів',
    selectAsset: 'Виберіть актив для виведення',
    disconnectWalletAlert:
      'Ви впевнені, що бажаєте відключити свій гаманець?\n\nБез підключеного гаманця ви не можете надіслати запит на зняття коштів!',
    history: 'Історія',
    details: {
      title: 'Деталі',
      amount: 'Сума',
      fee: 'Комісія',
      address: 'Адреса',
      status: 'Статус',
      lastUpdate: 'Останнє оновлення'
    },
    emptyHistory: 'Історії ще немає',
    withdraw: {
      title: 'Виведення',
      yourWallet: 'Твій гаманець',
      amount: 'Кількість',
      available: 'Доступно {amount}',
      minimum: 'Мінімум {amount} {currency}',
      successMessage:
        'Транзакція була\nуспішно відправлена.\n\nВиконання транзакції\nможе зайняти до 3 днів.'
    }
  },
  leagues: {
    title: 'Ліги',
    description: 'Рейтинг залежить від балансу квитків',
    league: 'Ліга',
    blocker: 'Досягніть {league},\nщоб розблокувати {feature}'
  },
  playerProfile: {
    title: 'Профіль',
    allTimeScore: 'Найкращий рахунок',
    averageScore: 'Середній рахунок',
    gamesPlayed: 'Зіграно ігор'
  },
  reward: {
    boxes: 'Лутбокси',
    customCoin: 'Шрек',
    dynamicCoins_1: 'Ракета',
    dynamicCoins_2: 'Мушля',
    fullLives: 'Повне життя',
    hard: 'Зірки',
    lives: 'Життя',
    magicHorns: 'Роги',
    refs: 'Друзі',
    refsFake: 'Друзі',
    rewards: 'Нагороди',
    soft: 'Монети',
    stackableAimbot: 'AIM Бот',
    stackableJumper: 'Пружина',
    stackableMagneticField: 'Магніт',
    tickets: 'Квитки',
    timeBoundAimbot: 'AIM Бот',
    timeBoundJumper: 'Пружина',
    timeBoundMagneticField: 'Магніт',
    title: 'Нагорода',
    ton: 'ТОН',
    unlimitedLives: 'Необмежені життя',
    wheelSpins: 'Колеса Фортуни',
    youGot: 'Ви отримали',
    puzzleCoins: 'Фрагмент'
  },
  magnetFields: {
    magnet: 'МАГНІТ',
    magnetic_field_1: 'МАЛИЙ',
    magnetic_field_2: 'ВЕЛИКИЙ'
  },
  dailyRewards: {
    title: 'Щоденні винагороди',
    info: 'Повертайся завтра за новими винагородами!',
    skinInfo: 'Заходьте щодня, щоб отримати {skin} Скін з бонусом {bonus}',
    day: 'День',
    almostThere: 'Ще трошки',
    youNeedRefs: 'Вам потрібно більше друзів, щоб розблокувати'
  },
  subscription: {
    description: 'Ви добре справляєтесь!',
    details: 'Щоб продовжити збирати TON \n— приєднуйтесь до нашого каналу'
  },
  achievementRewards: {
    newAchievement: 'Нове Досягнення'
  },
  onepercent: {
    description: 'Для отримання винагороди потрібно мати сумарний рахунок більше ніж {targetScore}',
    eventStartDescription:
      'Тобі потрібно мати сумарний рахунок\nбільше {targetScore} для винагороди',
    eventBlockedDescription:
      'Запроси принаймні 1 друга, щоб приєднатися до події. Або купи віртуального в нашому магазині. Призовий фонд:',
    eventEndDescription:
      'Подія завершилася!\nТи набрав сумарно {targetScore} балів.\nТвоя винагорода:'
  },
  hotrecord: {
    description: 'Більше рахунок - більша винагорода {rewardType}!',
    eventEndDescription: 'Подія завершилася!\nТвій рекорд {highScore}.\nТвоя винагорода:',
    letsGo: 'Вперед!'
  },
  reviveWindow: {
    title: 'Продовжити?',
    score: 'Рахунок: {score}',
    highScore: 'Рекорд: {score}',
    newHighscore: 'Новий рекорд {score}!',
    pointsLeftToHighscore: 'Необхідно лише {score} балів\nдля нового рекорду!',
    nextTONin: 'Лише {distance} балів щоб\nзловити TON!',
    nextCustomCoinIn: 'Лише {distance} балів щоб\nзловити Шрека!',
    saveMe: 'ДАЛІ?',
    maxRevive: 'Макс Спроб',
    freeRevive: 'Безплатно',
    revive: ' Грати',
    end: 'Вийти'
  },
  customCoin: {
    description:
      'Купіть {box} та виграйте скіни для фарму {x} більше монет! Змагайтеся за {ton} та {stars}',
    eventStartDescription: 'Вам потрібно мати більший рахунок, щоб отримати винагороду',
    boost: 'До {x} бонусу монет'
  },
  lives: {
    full: 'Макс',
    in: '+{amount} через',
    moreLives: 'Більше життів',
    noMoreLives: 'Немає життів',
    timeToNext: 'Час до наступного життя',
    inviteFriendToGet: ' Запроси друга, щоб отримати повні життя!',
    inviteFriend: 'Запросити друга',
    goToShop: 'Перейти до магазину'
  },
  joinCommunity: 'Приєднатися до спільноти',
  state: {
    yourScore: 'Твій рахунок',
    ticketsCollected: 'Зібрано квитків',
    coinsCollected: 'Зібрано монет'
  },
  tonEvent: {
    eventDescription:
      'Щоденний фонд: {limit}.\nЗагальний фонд подій: {totalLimit}.\nЗбільшуйте пул, запрошуючи друзів.\nКожні {friends} друзів +{bonus}!',
    eventLimitDescription: 'ЩОДЕННИЙ ПУЛ:',
    letsPlay: 'Грати!',
    nextPoolIn: 'Наступний пул:',
    eventNoTonAvailableDescription: 'Упс... Добовий ліміт ТОН досягнуто.\n Спробуйте завтра!',
    eventStartDescription: 'Скористайтеся своїм шансом зловити ТОН!',
    ton: 'ТОН'
  },
  tonLimit: {
    description: 'Ви зібрали {ton}\n Щоб зібрати більше TON розблокуйте\n {league}'
  },
  skinForTon: {
    description: 'Ексклюзивний скін за {ton}. Надішліть транзакцію та отримайте скін Bored Uni.'
  },
  reviveBanner: {
    description:
      'Коли ви впадете, ви можете використати Зірки, щоб продовжити стрибати та збирати більше TON!'
  },
  fragmentOffer: {
    description: 'Розблокуйте всю картинку, щоб отримати головний приз'
  },
  deepDiveOffer: {
    description: 'Завершіть, щоб отримати головний приз'
  },
  lootBoxOffer: {
    description:
      'Відкрийте 5 боксів з лімітованими скінами Губки та виграйте призи з пулу 30 TON та 20 000 Зірок.'
  },
  endsIn: 'До кінця ',
  nextFreeAfter: 'Наступний безплатний після:',
  connection: {
    title: "З'єднання втрачено",
    description: 'Перевірте підключення до Інтернету, щоб продовжити гру'
  },
  longLoad: {
    title: 'Важливо!',
    shortDescription: 'Завантаження триває довше, ніж очікувалося',
    fullDescription:
      "Будь ласка, переконайтеся, що у вас хороше з'єднання з мережею, і зачекайте, поки всі ресурси не будуть завантажені"
  },
  errors: {
    appVersion: 'Будь ласка, оновіть Telegram до останньої версії',
    walletNotConnectedForTransaction: "Потрібно під'єднати гаманець перед проведенням транзакції"
  },
  warnings: {
    inviteToCollect: 'Запроси більше друзів, щоб отримати'
  },
  contest: {
    successCaption: 'Вітаємо! Ви берете участь у розіграші!',
    failCaption:
      'Ви не виконали всі завдання. Будь ласка, перечитайте умови розіграшу та спробуйте ще раз!',
    errorCaption: 'Розіграш недоступний.',
    task: {
      tickets: 'Зберіть {value} квитків',
      friends: 'Запросіть {value} друзів',
      multiplier: 'Досягніть множника {value}',
      skin: "Відкрийте скін '{value}'",
      starsTotal: 'Витрачено загалом {value} зірок',
      starsDuringContest: 'Витрачено {value} зірок під час розіграшу'
    }
  },
  clans: {
    clans: 'Клани',
    myClan: 'Мій клан',
    title: 'Клан',
    topClans: 'Найкращі клани',
    event: {
      description: 'Збирай більше квитків, щоб виграти більше TON',
      name: 'Війни кланів',
      total_prize: 'Загальний приз\n{ton}',
      description_1: 'Беріть участь у подіях кланів\nта вигравайте до {ton}',
      requirenment: 'Запросіть більше друзів\nщоб почати подію'
    }
  },
  clan_event: {
    description: 'Збирай більше квитків, щоб виграти більше TON'
  },
  instructions: {
    hot_record: {
      1: 'Грайте в Uni Jump',
      2: 'Досягніть<br/><span class="instruction__step-text_yellow">МАКС</span> рекорду',
      3: 'Заробляйте зірки',
      4: '<span class="instruction__step-text_blue">1000</span> найкращих гравців<br/>отримають <span class="instruction__step-text_yellow">зірки</span>'
    },
    one_percent: {
      1: 'Грайте в Uni Jump',
      2: 'Досягніть <span class="instruction__step-text_yellow">найкращого</span> загального<br/>рахунку',
      3: 'Заробляйте зірки'
    },
    ton_mining: {
      1: 'Грайте в Uni Jump',
      2: 'Збирайте TON',
      3: 'Використовуйте МАГНІТ для<br/>збору <span class="instruction__step-text_yellow">X3</span> TON'
    },
    custom_coin: {
      1: 'Отримайте ексклюзивні скіни<br/>з лімітованих боксів',
      2: 'Грайте та збирайте монети - кожен скін відкриває кращий множник монет!',
      3: 'Більше монет — вищий ранг, кращі нагороди!'
    },
    leagues: {
      1: 'Покращте<br/><span class="instruction__step-text_yellow">X</span> множник',
      2: 'Збирайте більше<br/>квитків',
      3: 'Розблокуйте нові <span class="instruction__step-text_yellow">ліги</span>',
      4: 'Нові <span class="instruction__step-text_yellow">ліги</span> розблоковують<br/>більше подій!'
    },
    ice_fragment: {
      1: 'Грайте в Uni Jump',
      2: 'Збирайте монети',
      3: 'Отримуйте нагороди!'
    },
    clan_create: {
      1: 'Створіть<br/>групу Telegram',
      2: 'Додайте бота <span class="instruction__step-text_yellow">UniJump</span>',
      3: 'Надайте боту права адміністратора',
      4: 'Запрошуйте друзів до групи<br/>та розвивайте клан!'
    },
    clan_event: {
      1: 'Запрошуйте друзів<br/>до клану',
      2: 'Досягніть <span class="instruction__step-text_yellow">500</span> учасників<br/>3 ліги',
      3: 'Почніть<br/>подію',
      4: 'Отримайте нагороду <span class="instruction__step-text_blue">50 TON</span>!'
    }
  },
  hoursFull: 'Години',
  days: 'д',
  hours: 'г',
  minutes: 'хв',
  seconds: 'с',
  linkCopied: 'Скопійовано',
  error: 'Помилка',
  claimed: 'Claimed',
  canceled: 'Скасовано',
  success: 'Успішно',
  pending: 'В процесі',
  processing: 'Обробка',
  minutesFull: 'Хвилини',
  exploiters: {
    collectDescription: 'Гарна робота! Ваш TON тепер у безпеці. Заберіть його!',
    count: 'Вбити {count} Експлойтерів',
    heist: 'Пограбування',
    lastChance: 'ОСТАННІЙ ШАНС!',
    lastChanceDescription: 'Ой... У вас є останній шанс повернути TON! Спробуйте зараз.',
    task: 'Вбийте {count} за 1 гру',
    ton: 'ТОН',
    welcomeDescription: 'Увага! Експлойтери вкрали ваш TON. Поверніть його!'
  }
}
