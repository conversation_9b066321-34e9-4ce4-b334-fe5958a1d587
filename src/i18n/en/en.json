{"actions": {"play": "Play", "continue": "Continue", "claim": "<PERSON><PERSON><PERSON>", "getReward": "Get reward", "collect": "Collect", "tapToPlay": "Tap\nto play", "tapToContinue": "Tap\nto continue", "tapToJump": "Tap to Jump", "tapToShoot": "Tap to Shoot", "tapToCollect": "Tap to collect", "tapToOpen": "Tap to open", "tapToSkip": "Tap to skip", "connectWallet": "Connect Wallet", "makeTransaction": "Make Transaction", "cancelTransaction": "Cancel Transaction", "playAgain": "Play again", "allowAccess": "Allow access", "select": "Select", "subscribe": "Subscribe", "withdraw": "Withdraw", "back": "Back", "startFarming": "Farm", "leave": "Leave", "goToHome": "Go to Home", "goToShop": "Go to shop", "goToSkins": "Go to skins!", "disconnect": "Disconnect", "inviteFriend": "In<PERSON><PERSON>", "create": "Create", "hold": "Hold for autospin", "spin": "Spin", "stop": "Stop", "openChat": "Open Chat", "startEvent": "Start Event", "join": "Join", "got": "Got it!", "open_telegram": "Open Telegram", "lets_go": "Let's go!"}, "search": "Search", "boosters": {"title": "Boosters", "stackableJumper": {"name": "Spring", "fullName": "Spring", "description": "Boosts your coin collection, maximizing your earnings per session."}, "stackableAimbot": {"name": "AIM <PERSON>", "fullName": "AIM <PERSON>", "description": "Boosts your coin collection, maximizing your earnings per session."}, "stackableMagneticField": {"name": "<PERSON><PERSON><PERSON>", "fullName": "Magnetic Field", "description": "Boosts your coin collection, maximizing your earnings per session."}, "endlessRun": "Endless Run", "rewards": "Rewards you can get:", "select": "Select boosters:"}, "warning": "Warning", "mainMenu": "Main menu", "pausedOnBack": "Are you sure you want to leave the game?", "gameover": "Game over", "totalScore": "Total Score", "currentScore": "Current score:", "allowGyroscope": "Allow\nthe Gyroscope", "free": "Free", "selected": "Selected", "hotRecord": "Hot Record", "joinUs": "Join us!", "joinCommunity": "Join community", "termsOfUse": "Terms of Use", "faq": "FAQ", "swipeScreen": "Swipe the\nscreen to move", "event_ends_at": "Event ends at: {time}", "event_ended": "Event ended", "total_prize_pool": "Total prize pool", "features": {"farming": "Farming", "dailyReward": "Daily Rewards", "onePercentEvent": "1% Event", "hotRecordEvent": "Hot Record", "tonMiningEvent": "Ton Mining", "withdraw": "Withdraw", "customCoinEvent": "Skin Event", "dynamicCoins_1": "To the Moon", "dynamicCoins_2": "Deep Dive", "lives": "Lives", "iceFragment": "Ice Fragment", "clanEvent": "Clan Event", "battleEvent": "Battle Event"}, "skins": {"title": "Skins", "description": "Skin multipliers stack — buy more to boost ticket X", "yourSkins": "Your skins: {amount}/{total}", "newSkin": "You got new skin!", "list": {"-1": {"title": "Uni", "description": "<PERSON><PERSON> is the ultimate dreamer, believing in the impossible and inspiring everyone around!"}, "0": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> is spinning into chaos, ready for a wild ride!"}, "1": {"title": "Emo-Uni", "description": "<PERSON><PERSON> doesn't smile, but it's all about the feels."}, "2": {"title": "Uni Plumber", "description": "Serious plumbing skills, magical twist — Uni Plumber at your service!"}, "3": {"title": "Uni Kitty", "description": "Uni Kitty is the purr-fect blend of fun and adventure!"}, "4": {"title": "Uni Ninja", "description": "Silent and swift, Uni Ninja is always one step ahead."}, "5": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Fries first, questions later — <PERSON><PERSON><PERSON><PERSON> is busy eating!"}, "6": {"title": "<PERSON><PERSON>", "description": "With every Uni Potter leap, the world fills with magic!"}, "7": {"title": "Devil Uni", "description": "Ready for some trouble? Devil Uni is on the case!"}, "8": {"title": "Super Uni", "description": "Super Uni is here to save the day!"}, "9": {"title": "Resistance Uni", "description": "Join Digital Resistance with Resistance Uni!"}, "10": {"title": "Uni Diver", "description": "Uni Diver leaves behind bubble trails everywhere, literally!"}, "11": {"title": "Chicken Uni", "description": "<PERSON> is a feathered friend, making each journey egg-citing!"}, "12": {"title": "Capy Uni", "description": "Relax mode: ON<PERSON> <PERSON><PERSON> brings calm vibes to every journey."}, "13": {"title": "<PERSON><PERSON>", "description": "Smelly but proud, <PERSON><PERSON> isn't afraid to be himself!"}, "14": {"title": "Pepe Uni", "description": "Adventures are better with <PERSON><PERSON><PERSON>'s classic look!"}, "1000": {"title": "Uni Inu", "description": "Bark, sparkle, repeat — <PERSON><PERSON> is unstoppable!"}, "1001": {"title": "<PERSON><PERSON>", "description": "\"Make Adventures Great Again\" is the motto of Uni Trump!"}, "1002": {"title": "Chainsaw Uni", "description": "Chainsaw Uni turns obstacles into sawdust. Bzzz!"}, "1003": {"title": "SWAG Uni", "description": "SWAG Uni — gold, glam, and instant charm!"}, "1004": {"title": "Uni Fremen", "description": "Jump for freedom, smash every monster!"}, "1005": {"title": "Grump Uni", "description": "Jump, fall, sigh… Repeat. The saddest ticket collector in the Universe"}, "1006": {"title": "<PERSON>", "description": "Jumps fast, falls hard, complains always. Big drama, small bird!"}, "1007": {"title": "Uni Duo", "description": "Funny owl teaching you how to jump. Don't skip her lessons."}, "1008": {"title": "Spring Uni", "description": "Fresh, funny and loves green tickets."}, "1009": {"title": "Uni Magic", "description": "<PERSON> Uni with some magic, but not much luck."}, "1010": {"title": "<PERSON><PERSON> Luck", "description": "Rich and lucky, old <PERSON><PERSON> who catches 10X more coins!"}, "2000": {"title": "Chill Uni", "description": "Always calm, always stylish. His cool mind is the key to victory!"}, "2001": {"title": "Mystic Uni", "description": "Master of magic and mysteries. One movement – and reality changes!"}, "2002": {"title": "Uni Beat", "description": "He doesn't just listen to music – he controls it!"}, "2003": {"title": "Uni Relic", "description": "The ancient spirit that has returned. Its power is beyond time!"}, "2004": {"title": "Uni Reaper", "description": "A mysterious souls hunter that comes on the darkest nights."}, "2005": {"title": "Uni Inferno", "description": "A real fire demon that burns everything in its path."}, "2006": {"title": "Uni Bera", "description": "Cute Bera with Ooga Booga vibes"}, "2007": {"title": "Ponke Uni", "description": "Mad, degen, and always craving tickets"}, "2008": {"title": "Uni Bull", "description": "Jumps UP Only like every bull market"}, "2009": {"title": "Uni Doge", "description": "Woof woof, pump and collect"}, "2010": {"title": "Uni Popcat", "description": "Eats TON with wide open mouth"}, "2011": {"title": "Uni Pengu", "description": "Soft vibes, funny moves, big wins"}, "2012": {"title": "<PERSON><PERSON>", "description": "Jumps for fries and cola, but collects tickets."}, "2013": {"title": "Uni Art", "description": "Smiles when Exploiters scream, and collects your tickets."}, "2014": {"title": "<PERSON><PERSON>", "description": "The face remains hidden, the loyalty — never!"}, "2015": {"title": "Uni Shroom", "description": "Cap so red, Airdrop ahead!"}, "2016": {"title": "Uni Princess", "description": "Jumps so clean, like a queen!"}, "2017": {"title": "Uni Cloud", "description": "Soft and high, TON from sky!"}, "2018": {"title": "Uni Fairy", "description": "Liquid power, tickets shower!"}, "2019": {"title": "Uni Mixture", "description": "Magic charm, Airdrop farm!"}, "2020": {"title": "Uni Genie", "description": "Wish comes true—TON for you!"}, "2021": {"title": "Uni Rubik", "description": "Twist and turn, TON to earn!"}, "2022": {"title": "Uni Gift", "description": "Unwrap the fun, gifts for everyone!"}, "2023": {"title": "Uni Rich", "description": "Gold on top, farming Airdrop nonstop!"}, "2024": {"title": "Uni Neko", "description": "Paw for luck, <PERSON><PERSON> gets stuck!"}, "2025": {"title": "Uni Rabbit", "description": "Fast and free, tickets for me!"}, "2026": {"title": "Uni Dice", "description": "Jump and spin, lucky win!"}, "2027": {"title": "Uni Radio", "description": "Signal strong, TON all day long!"}, "2028": {"title": "Uni NightSky", "description": "Night so clear, TON is near!"}, "2029": {"title": "Uni Lumpy", "description": "Let the lumps guide you to epic rewards!"}, "2030": {"title": "Uni Rocket", "description": "Flying To The Moon while pumping UNICOIN!"}, "2031": {"title": "Uni Luna", "description": "Magic Moon cat transforming jumps into tickets."}, "2032": {"title": "Astro Uni", "description": "Jumps so high that can't breath without a costume."}, "2033": {"title": "Uni Basket", "description": "Bonus-packed and ready to jump"}, "2034": {"title": "Chicken Uni", "description": "Can't fly, but jumps like a champ!"}, "2035": {"title": "<PERSON><PERSON>", "description": "Hop-hop to the top!"}, "2036": {"title": "<PERSON><PERSON>", "description": "Sweet, orange, unstoppable."}, "2037": {"title": "Uni Egg", "description": "What's inside? Only jumping reveals!"}, "2038": {"title": "<PERSON><PERSON>", "description": "A flower with jump power."}, "2039": {"title": "Uni Citizen", "description": "Basic skin, still a win!"}, "2040": {"title": "Uni TNT", "description": "Jump, collect, and Boom connect!"}, "2041": {"title": "Uni Zombie", "description": "From grave to sky, he flies high!"}, "2042": {"title": "Uni Diamond", "description": "<PERSON><PERSON> set, best jump yet!"}, "2043": {"title": "<PERSON><PERSON>", "description": "From cube to cloud, <PERSON> jumps proud!"}, "2044": {"title": "<PERSON><PERSON>", "description": "Creepy and cool, breaking every rule!"}, "2045": {"title": "Uni Creeper", "description": "One big boom, clears the room!"}, "2046": {"title": "<PERSON><PERSON>", "description": "With big-eyed cheer, <PERSON><PERSON> is near!"}, "2047": {"title": "<PERSON><PERSON>", "description": "Brave and slick, jumps that stick!"}, "2048": {"title": "Uni Balthazar", "description": "Dance and spin, jump to win!"}, "2049": {"title": "<PERSON><PERSON>", "description": "Yellow and wild, jumpin' child!"}, "2050": {"title": "Uni Evil", "description": "Purple beast, ticket feast!"}, "2051": {"title": "Uni Gru", "description": "Evil plan, TON for the clan!"}, "2052": {"title": "Uni Vector", "description": "Vector's plan? Catch if you can!"}, "2053": {"title": "<PERSON><PERSON>", "description": "Jump and dive, feel alive!"}, "2054": {"title": "Jedi Uni", "description": "Force in the air, tickets everywhere!"}, "2055": {"title": "Sith Uni", "description": "Shadows call, tickets fall!"}, "2056": {"title": "Uni Storm", "description": "Shooting in monsters – targeting tickets!"}, "2057": {"title": "<PERSON><PERSON><PERSON>i", "description": "Small and quick, magic trick!"}, "2058": {"title": "Wookiee Uni", "description": "Big and loud, jumps proud!"}, "2059": {"title": "Mando Uni", "description": "Catching tickets - this is the way!"}, "2060": {"title": "Captain <PERSON><PERSON>", "description": "Leap with might, win the fight!"}, "2061": {"title": "<PERSON>", "description": "Big green hop, can't stop!"}, "2062": {"title": "Iron Uni", "description": "Armor on, jumping strong!"}, "2063": {"title": "<PERSON><PERSON> Loki", "description": "Laugh and flee, ticket spree!"}, "2064": {"title": "<PERSON>", "description": "Web of luck, tickets stuck!"}, "2065": {"title": "<PERSON><PERSON>", "description": "Power punch, big ticket bunch!"}, "2066": {"title": "<PERSON>", "description": "Thunder power, ticket shower!"}, "2067": {"title": "Uni Po", "description": "Hero of snacks, fights with whacks!"}, "2068": {"title": "<PERSON><PERSON>", "description": "Calm and quick, master trick!"}, "2069": {"title": "Mr. <PERSON>", "description": "Bowl jump fun, tickets run!"}, "2070": {"title": "<PERSON>", "description": "Horned and mean, TON machine!"}, "2071": {"title": "Tai Lung Uni", "description": "Cold and bold, TON unfolds!"}, "2072": {"title": "Tigress Uni", "description": "Eyes that burn, watch her turn!"}, "2073": {"title": "<PERSON><PERSON><PERSON>", "description": "Wise and slow, TON will grow!"}, "2074": {"title": "<PERSON>", "description": "Slow but sure, TONs secure!"}, "2075": {"title": "Krabs Uni", "description": "Shell so slick, TO<PERSON> comes quick!"}, "2076": {"title": "<PERSON>", "description": "Starry spin, tickets win!"}, "2077": {"title": "Sponge Uni", "description": "Jelly jump, tickets pump!"}, "2078": {"title": "Plankton Uni", "description": "Secret plan, tickets in the pan!"}, "2079": {"title": "<PERSON>", "description": "Jumps with brains, TON in chains!"}, "2080": {"title": "<PERSON><PERSON><PERSON>", "description": "Grumpy dash, still gets cash!"}, "2081": {"title": "Shrek Uni", "description": "Mud and might, tickets in sight!"}, "2082": {"title": "Dragon Uni", "description": "Guard the TON, tickets on!"}, "2083": {"title": "<PERSON>", "description": "Royal leap, tickets to keep!"}, "2084": {"title": "<PERSON><PERSON><PERSON>", "description": "Bite and bounce, tickets count!"}, "2085": {"title": "Pinocchio Uni", "description": "Jump and lie, tickets fly!"}, "2086": {"title": "Donkey Uni", "description": "Talks nonstop, jumps don’t drop!"}, "2087": {"title": "Red Cat Uni", "description": "Bite and bounce, tickets count!"}, "2088": {"title": "<PERSON>", "description": "Bright, quick, and full of bounce!"}, "2089": {"title": "Uni Green", "description": "<PERSON><PERSON> green, scores clean."}, "2090": {"title": "Uni Aquaman", "description": "From deep sea to top scores."}, "2091": {"title": "Uni Batman", "description": "Silent jumper of the TON night."}, "2092": {"title": "Uni Cyborg", "description": "Target locked: TON."}, "2093": {"title": "Uni Flash", "description": "Fastest jumper in the Uni-Verse!"}, "2094": {"title": "Uni Wonder", "description": "Wonder in name, wonder in wins!"}, "2095": {"title": "<PERSON><PERSON>", "description": "Team first, tickets always."}, "2096": {"title": "Uni Velma", "description": "Uni brainpower = leaderboard power."}, "2097": {"title": "<PERSON><PERSON>", "description": "TON’s most glamorous chaser!"}, "2098": {"title": "<PERSON><PERSON>", "description": "Ruh-roh! Another Airdrop? Let’s go!"}, "2099": {"title": "<PERSON><PERSON>", "description": "Elegant. Fast. Merciless."}, "2100": {"title": "Uni Sub-Zero", "description": "Ice-cold jumps. Hot rewards"}, "2101": {"title": "Uni Kitana", "description": "Jumps that slice through scores!"}, "2102": {"title": "<PERSON><PERSON> Scor<PERSON>", "description": "Flames and jumps. No mercy."}, "2103": {"title": "Uni Cat", "description": "Lead the army of cats to win the battle."}, "2104": {"title": "Uni Dog", "description": "Prove that dogs are better than cats."}, "3000": {"title": "<PERSON><PERSON>", "description": "Every jump is a gamble, every fall is a lesson."}}, "requirenments": {"inviteFriend": "Invite more friends: {amount}", "wallet": "Connect wallet", "transaction": "Make first transaction", "box": "Open {boxType}", "dailyReward": "Days to earn: {days}", "inProgress": "In progress..."}}, "achievements": {"title": "Achievements", "description": "Get a bonus for your multiplier!", "completed": "Completed", "tapToClaim": "Tap to claim", "list": {"1": {"1": {"name": "Happy Together", "description": "Invite 1 referral"}, "2": {"name": "Local Boss", "description": "Invite 10 referrals"}, "3": {"name": "Referral Flexer", "description": "Invite 100 referrals"}, "4": {"name": "Influencer", "description": "Invite 500 referrals"}, "5": {"name": "Leader of Unicorns", "description": "Invite 1000 referrals"}}, "2": {"1": {"name": "Oops...", "description": "Revive 1 time"}, "2": {"name": "More than a cat", "description": "Revive 10 times"}, "3": {"name": "I'll be back", "description": "Revive 100 times"}, "4": {"name": "Not today", "description": "Revive 1 000 times"}, "5": {"name": "Fenix", "description": "Revive 10 000 times"}}, "3": {"1": {"name": "New addiсtion", "description": "High score: \n10 000"}, "2": {"name": "Jump Student", "description": "High score: \n100 000"}, "3": {"name": "Up! Up! Up!", "description": "High score: \n300 000"}, "4": {"name": "Jump Master", "description": "High score: \n500 000"}, "5": {"name": "To The Moon", "description": "High score: \n1 000 000"}}, "4": {"1": {"name": "Poor Student", "description": "Total soft currency spent: \n10 000"}, "2": {"name": "<PERSON>", "description": "Total soft currency spent: \n100 000"}, "3": {"name": "<PERSON><PERSON><PERSON> Mc<PERSON>uck", "description": "Total soft currency spent: \n500 000"}, "4": {"name": "Gold Millionaire", "description": "Total soft currency spent: \n1 000 000"}, "5": {"name": "Golden Card", "description": "Total soft currency spent: \n2 000 000"}}, "5": {"1": {"name": "<PERSON><PERSON>", "description": "Jump on 10 monsters"}, "2": {"name": "<PERSON><PERSON>, Squished", "description": "Jump on 30 monsters"}, "3": {"name": "Jumpocalypse", "description": "Jump on 50 monsters"}, "4": {"name": "<PERSON><PERSON><PERSON>", "description": "Jump on 100 monsters"}, "5": {"name": "Smashed Snacks", "description": "Jump on 300 monsters"}}, "6": {"1": {"name": "Ouch Maker", "description": "Shoot 10 monsters "}, "2": {"name": "Shotgun Surgeon", "description": "Shoot 30 monsters "}, "3": {"name": "Needlework", "description": "Shoot 50 monsters "}, "4": {"name": "Hit and Run", "description": "Shoot 100 monsters "}, "5": {"name": "Hit Parade", "description": "Shoot 300 monsters "}}, "7": {"1": {"name": "Boosted to the Moon", "description": "Use propeller/jetpack 3 times in a game"}, "2": {"name": "Too Much Juice", "description": "Use propeller/jetpack 5 times in a game"}, "3": {"name": "Boosted Beyond Belief", "description": "Use propeller/jetpack 10 times in a game"}}, "8": {"1": {"name": "First Blood", "description": "Kill 100 monsters"}, "2": {"name": "Angry Killer", "description": "Kill 500 monsters"}, "3": {"name": "<PERSON><PERSON><PERSON>", "description": "Kill 1 000 monsters"}, "4": {"name": "Terminator", "description": "Kill 5 000 monsters"}, "5": {"name": "Beast Mode On", "description": "Kill 10 000 monsters"}}, "9": {"1": {"name": "Platform Smasher", "description": "Break 10 fragile platforms in a game"}, "2": {"name": "<PERSON><PERSON>", "description": "Break 50 fragile platforms in a game"}, "3": {"name": "Grounded too soon", "description": "Break 100 fragile platforms in a game"}, "4": {"name": "I broke it again", "description": "Break 300 fragile platforms in a game"}, "5": {"name": "Falling Star", "description": "Break 500 fragile platforms in a game"}}, "10": {"1": {"name": "Engine warm-up", "description": "Fly with jetpack 10 times"}, "2": {"name": "Ace Pilot", "description": "Fly with jetpack 50 times"}, "3": {"name": "Lift Off", "description": "Fly with jetpack 200 times"}, "4": {"name": "Jetpack Junkie", "description": "Fly with jetpack 1 000 times"}, "5": {"name": "Altitude Addict", "description": "Fly with jetpack 2 500 times"}}, "11": {"1": {"name": "Fan-tastic game", "description": "Fly with propeller 10 times"}, "2": {"name": "Drone Pilot", "description": "Fly with propeller 50 times"}, "3": {"name": "Wind Power Wizard", "description": "Fly with propeller 200 times"}, "4": {"name": "Airstream King", "description": "Fly with propeller 1 000 times"}, "5": {"name": "Propeller Pro 2500", "description": "Fly with propeller 2 500 times"}}, "12": {"1": {"name": "New Wardrobe", "description": "Unlock 1 skin"}, "2": {"name": "Say yes to the skin", "description": "Unlock 5 skin"}, "3": {"name": "Swag", "description": "Unlock 10 skin"}, "4": {"name": "Elite Style Collector", "description": "Unlock 20 skin"}, "5": {"name": "God of Style", "description": "Unlock 40 skin"}}, "13": {"1": {"name": "Score Noob", "description": "Total score across all games: \n10 000"}, "2": {"name": "Collect 'Em All", "description": "Total score across all games: \n100 000"}, "3": {"name": "Sleep is Overrated", "description": "Total score across all games: \n1 000 000"}, "4": {"name": "Grind Mode Activated", "description": "Total score across all games: \n10 000 000"}, "5": {"name": "Forbes 100 under 100", "description": "Total score across all games: \n100 000 000"}}, "14": {"1": {"name": "Hoppin' Around", "description": "Make 1 000 jumps"}, "2": {"name": "<PERSON><PERSON><PERSON>", "description": "Make 10 000 jumps"}, "3": {"name": "Skywalker in Training", "description": "Make 100 000 jumps"}, "4": {"name": "First class jumper", "description": "Make 250 000 jumps"}, "5": {"name": "Sky's the Limit", "description": "Make 1 000 000 jumps"}}, "15": {"1": {"name": "<PERSON><PERSON><PERSON>", "description": "Jump 10 times on a trampoline"}, "2": {"name": "Up, Down, Try Again", "description": "Jump 100 times on a trampoline"}, "3": {"name": "Springing Into Trouble", "description": "Jump 1 000 times on a trampoline"}, "4": {"name": "Knees Still Bouncing", "description": "Jump 10 000 times on a trampoline"}, "5": {"name": "Trampoline Master", "description": "Jump 100 000 times on a trampoline"}}, "16": {"1": {"name": "Spring Along Rookie", "description": "Jump 10 times on a spring"}, "2": {"name": "<PERSON><PERSON><PERSON>", "description": "Jump 100 times on a spring"}, "3": {"name": "Knees are done", "description": "Jump 1 000 times on a spring"}, "4": {"name": "I can't do it anymore", "description": "Jump 10 000 times on a spring"}, "5": {"name": "Who Needs Trampolines?", "description": "Jump 100 000 times on a spring"}}}}, "lootboxes": {"rainbowLootBox": "Rainbow Box", "luckyLootBox": "Lucky Box", "cryptoLootBox": "Crypto Box", "moonLootBox": "Moon Box", "easterLootBox": "Easter Box", "minecraftLootBox": "Craft Box", "bananaLootBox": "Banana Box", "starWarsLootBox": "Star Wars Box", "infinityLootBox": "Infinity Box", "kungFuLootBox": "Kung Fu Box", "spongeLootBox": "Sponge Box", "shrekLootBox": "<PERSON><PERSON><PERSON>'s Box", "justiceLootBox": "Justice Box", "scoobyLootBox": "Scooby Box", "combatLootBox": "Kombat Box"}, "nextFreeAfter": "Next free after:", "soon": "Soon", "portraitOrientationRequired": "Use portrait orientation for better experience", "loading": "Loading", "settings": {"title": "Settings", "music": "Music", "sound": "Sound", "haptic": "Vibration", "language": "Language", "support": "Support"}, "farming": {"start": "Start Farming", "farming": "Farming"}, "friends": {"title": "Friends", "inviteFriends": "Invite Friends", "description": "Get a bonus for each friend invited!", "invitedCount": "Your referrals: {count}", "freeInviteDesc": "Per friend", "premiumInviteDesc": "Per friend with Premium", "emptyRefs": "Invite friends to get juicy\nrewards and have fun together"}, "earn": {"title": "<PERSON><PERSON><PERSON>", "name": "Tasks", "description": "Complete tasks and earn rewards", "timeToVerify": "Task verification time 1h", "startTasks": "Start Tasks", "dailyTasks": "Daily Tasks", "completedMissions": "Completed Missions", "tasksCompleted": "Tasks completed:", "missions": {"title": "Missions", "partners": "Partners", "battle_event": {"jumping": {"name": "Boost Jumping skill"}, "luck": {"name": "<PERSON><PERSON>"}, "power": {"name": "Boost Power"}, "magic": {"name": "Boost Magic"}}, "invite_5_friend": {"name": "Invite 5 friends", "action": "Invite"}, "invite_3_friend": {"name": "Invite 3 friends", "action": "Invite"}, "invite_1_friend": {"name": "Invite 1 friend", "action": "Invite"}, "connect_wallet": {"name": "Connect TON wallet", "action": "Connect"}, "first_transaction": {"name": "Make first TON transaction", "action": "Make transaction"}, "subscribe_main_channel": {"name": "Subscribe to the main channel", "action": "Subscribe"}, "use_booster": {"name": "Catch {goal} boosters", "action": "Play"}, "jump_to_score": {"name": "Score {goal} in one game", "action": "Play"}, "kill_monster": {"name": "Kill {goal} monsters", "action": "Play"}, "invite_ref": {"name": "Invite {goal} friend(s)", "action": "Invite"}, "play_game": {"name": "Play {goal} game(s)", "action": "Play"}, "catch_ticket": {"name": "Catch {goal} Ticket", "action": "Play"}, "daily_total_jump": {"name": "Jump {goal} point in total", "action": "Play"}, "use_aimbot_booster": {"name": "Use {goal} AIM Bot", "action": "Play"}, "use_jumper_booster": {"name": "Use {goal} Spring", "action": "Play"}, "use_magnet_booster": {"name": "Use {goal} Magnet", "action": "Play"}, "unlock_league": {"name": "Unlock {goal} League", "action": "Play"}, "buy_skin": {"name": "Buy a skin", "action": "Go to skins"}, "use_revive": {"name": "Use {goal} revive(s)", "action": "Play"}, "subscribe_x": {"name": "Follow our X", "action": "Follow"}, "subscribe_community_chat": {"name": "Join Uni Jump community group", "action": "Join"}, "add_to_home_screen": {"name": "Add Uni Jump to the home screen", "action": "Add to home screen"}, "purchase_in_shop_for_stars": {"name": "Spend 100 Stars in the Store", "action": "Purchase"}, "purchase_skin_for_stars": {"name": "Purchase any Skin for Stars", "action": "Purchase"}, "boost_telegram_channel": {"name": "Boost our Telegram channel", "action": "Boost"}, "go_to_miniapp_9": {"name": "Play Match-3, watch ads, and earn USDt!", "action": "Play MatchMoney"}, "go_to_miniapp_10": {"name": "Boinker: spin the slot and collect Artifacts", "action": "Join <PERSON>"}, "go_to_miniapp_15": {"name": "Play <PERSON>r to win $300", "action": "Play Miner"}, "go_to_miniapp_19": {"name": "Open chests — get USDT", "action": "Join Digger game"}, "go_to_miniapp_22": {"name": "Start merging cars today!", "action": "Join DRFT Party"}, "go_to_miniapp_23": {"name": "You Play - We Really Pay!", "action": "Join <PERSON>"}, "go_to_miniapp_24": {"name": "Play Outmine", "action": "Join <PERSON>"}, "go_to_miniapp_25": {"name": "Breed ducks to earn $EGG tokens!", "action": "Play <PERSON>yGram"}, "go_to_miniapp_26": {"name": "Launch Symptomify", "action": "Play Symptomify"}, "go_to_miniapp_27": {"name": "Play WORK DOGS", "action": "Join WORK DOGS"}, "go_to_miniapp_28": {"name": "Tap & Earn $HASH", "action": "Play HashCats"}, "go_to_miniapp_29": {"name": "Check in, open boxes & get $RICH", "action": "Join <PERSON>"}, "go_to_miniapp_30": {"name": "Play Simple Tap", "action": "Join Simple Tap"}, "go_to_miniapp_31": {"name": "Play Pookie & get TON boxes!", "action": "Play Pookie Cheese"}, "go_to_miniapp_32": {"name": "Mine TON in TonTower", "action": "Play TonTower"}, "go_to_miniapp_33": {"name": "Play Fasqon & eran FSQN tokens", "action": "<PERSON><PERSON>"}, "go_to_miniapp_34": {"name": "Play Capybara MEME", "action": "Join <PERSON>"}, "go_to_miniapp_35": {"name": "Launch 🚀 and withdraw $$$", "action": "Join Space Adventure"}, "go_to_miniapp_36": {"name": "Get Free Stars🌟", "action": "Join <PERSON>"}, "go_to_miniapp_37": {"name": "Join Daily Combo Updates", "action": "Join Daily Combo"}, "go_to_miniapp_38": {"name": "Play JustFab", "action": "Join <PERSON>"}, "go_to_miniapp_39": {"name": "Join T<PERSON>", "action": "Join T<PERSON>"}, "go_to_miniapp_40": {"name": "Play Pixiland & Claim $wPIXI now!", "action": "Join <PERSON>"}, "go_to_miniapp_41": {"name": "Play Biz Tycoon Now", "action": "Join <PERSON>  "}, "go_to_miniapp_42": {"name": "Join <PERSON><PERSON>", "action": "Join <PERSON><PERSON>"}, "go_to_miniapp_43": {"name": "Join <PERSON> and spin 1 slot", "action": "Join <PERSON>"}, "go_to_miniapp_44": {"name": "<PERSON><PERSON>", "action": "<PERSON><PERSON>"}, "go_to_miniapp_45": {"name": "Join Pokergram & Grab your free chips now!", "action": "Join <PERSON>"}, "go_to_miniapp_46": {"name": "Play Agent301", "action": "Play Agent301"}, "go_to_miniapp_47": {"name": "Get Telegram gift at Empty", "action": "Join Empty"}, "go_to_miniapp_48": {"name": "Earn USDT in GenkiMiner!", "action": "Join <PERSON>"}, "go_to_miniapp_49": {"name": "Play <PERSON><PERSON> in early access", "action": "Join <PERSON>"}}}, "shop": {"title": "Shop", "friendsDescription": "Buy a virtual friend and get \ninstant bonuses like a real one!", "bestDeal": "Best Deal", "new": "NEW", "free": "FREE", "boxDescription": "You can get one of this rewards", "nextFree": "Next free after"}, "airdrop": {"button": "Drop", "title": "Airdrop", "tasks": {"info": "Complete tasks to participate in the Airdrop!", "connectWallet": "Connect your TON wallet", "transaction": "Make TON transaction"}, "ticketsBanner": "Tickets is the only way to get Airdrop, more tickets — more rewards", "instructionSteps": {"1": "1. Connect your TON wallet", "2": "2. Let's make your first transaction to prove you are ready for Airdrop! It costs only 0.5 TON."}, "instructionText": "You can get TON for your wallet from any of the following exchanges:\n      Binance, Bybit, KuCoin, OKX or Bitget.", "comingSoon": "AIRDROP IS COMING SOON!"}, "wallet": {"title": "Wallet", "assets": "Assets", "connectWallet": "Сonnect your wallet to withdraw", "selectAsset": "Select an asset to withdraw", "disconnectWalletAlert": "Are you sure you want to disconnect your wallet?\n\nWithout a connected wallet, you cannot request a withdrawal!", "history": "History", "details": {"title": "Details", "amount": "Amount", "fee": "Fee", "address": "Address", "status": "Status", "lastUpdate": "Last Update"}, "emptyHistory": "No history yet", "withdraw": {"title": "Withdraw", "yourWallet": "Your wallet", "amount": "Amount", "available": "Available {amount}", "minimum": "Minimum {amount} {currency}", "successMessage": "<PERSON><PERSON><PERSON> has been\nsuccessfully sent.\n\nIt can take up to 3 days to\nprocess the transaction."}}, "leagues": {"title": "Leagues", "league": "League", "description": "Rating is based on the tickets balance", "blocker": "Reach {league}\nto unlock {feature}"}, "playerProfile": {"title": "Profile", "allTimeScore": "All Time High Score", "averageScore": "Average Score", "gamesPlayed": "Games Played"}, "reward": {"title": "<PERSON><PERSON>", "youGot": "You got", "tickets": "Tickets", "soft": "Coins", "hard": "Stars", "magicHorns": "Horns", "refs": "Friends", "refsFake": "Friends", "boxes": "Boxes", "stackableMagneticField": "<PERSON><PERSON><PERSON>", "stackableJumper": "Spring", "stackableAimbot": "AIM <PERSON>", "timeBoundMagneticField": "<PERSON><PERSON><PERSON>", "timeBoundJumper": "Spring", "timeBoundAimbot": "AIM <PERSON>", "unlimitedLives": "Unlimited lives", "fullLives": "Full lives", "ton": "TON", "lives": "Lives", "customCoin": "Paws Coin", "dynamicCoins_1": "Moon Coin", "dynamicCoins_2": "Shell Coin", "puzzleCoins": "Fragment", "rewards": "Rewards", "wheelSpins": "Wheel Spins"}, "magnetFields": {"magnet": "MAGNET", "magnetic_field_1": "SMALL", "magnetic_field_2": "LARGE"}, "dailyRewards": {"title": "Daily Rewards", "info": "Come back tomorrow for the new rewards!", "skinInfo": " Login daily and get {skin} Skin with {bonus} ticket bonus", "day": "Day", "almostThere": "Almost there", "youNeedRefs": "You need more friends to unlock"}, "subscription": {"description": "You're doing well!", "details": "To continue collect TON \n— join our channel"}, "achievementRewards": {"newAchievement": "New Achievement"}, "onepercent": {"description": "You need to have more than {targetScore} total score to get rewarded", "eventStartDescription": "You need to have more than\n{targetScore} total score to get", "eventBlockedDescription": "Invite at least 1 friend to join the event. Or buy a virtual one in our shop. Prize pool:", "eventEndDescription": "The event is over!\nYou scored {targetScore} points in total.\nYour reward:"}, "hotrecord": {"description": "You need to have more high score to get reward", "eventEndDescription": "The event is over!\nYour record is {highScore}.\nYour reward:", "letsGo": "Let's go!"}, "customCoin": {"description": "Buy {box} and win skins to farm {x} more coins! Compete for {ton} and {stars}", "eventStartDescription": "You need to have more high score to get reward", "boost": "Up to {x} Coin boost"}, "battle_event": {"team_select": "Choose your team:", "team_score": "Team Score", "boost": {"title": "Boost", "description": "Boost. E<PERSON><PERSON>. Win TON.", "instruction": "Collect up to 50x more coins with boost!"}}, "tonEvent": {"ton": "TON", "nextPoolIn": "Next pool in", "eventLimitDescription": "DAILY POOL: ", "letsPlay": "Let's play!", "eventStartDescription": "Take your chance and try to catch <PERSON><PERSON>!", "eventDescription": "The daily pool: {limit}.\nTotal event pool: {totalLimit}.\nIncrease the pool by inviting friends.\nEvery {friends} friends +{bonus}!", "eventNoTonAvailableDescription": "Oops... The daily TON limit is reached.\n Try again tomorrow!"}, "clan_event": {"description": "Collect more tickets to win more TON"}, "tonLimit": {"description": "You've collected {ton}\n To collect more TON unlock \n {league}"}, "skinForTon": {"description": "Buy exclusive skin for {ton} Just sent a transaction and claim unique Bored Uni skin."}, "reviveBanner": {"description": "Once you fall you can use Stars to continue jumping and collect more TON!"}, "deepDiveOffer": {"description": "Complete to collect the grand prize"}, "fragmentOffer": {"description": "Unlock the entire picture to get the grand prize"}, "lootBoxOffer": {"description": "Open 5 Boxes with limited Mortal Kombat Skins and win prizes from 30 TON and 20 000 Stars Pool."}, "endsIn": "Ends in ", "exploiters": {"ton": "TON", "heist": "Heist", "task": "Kill {count} in 1 game", "count": "Kill {count} Exploiters", "lastChance": "LAST CHANCE!", "collectDescription": "Nice job! Your TON is safe now. Collect it!", "lastChanceDescription": "Oops... You have the last chance to get TON back! Try now.", "welcomeDescription": "Warning! Exploiters have stolen your TON. Get it back!"}, "controlMethod": {"gyroscope": "Gyroscope", "swipe": "Swipe", "selectMsg": "Select your preferable type of control", "selected": "Selected", "select": "Select"}, "reviveWindow": {"title": "Continue?", "score": "Score: {score}", "highScore": "High Score: {score}", "newHighscore": "New High Score {score}!", "pointsLeftToHighscore": "Only {score} points to beat\nyour High Score!", "nextTONin": "Only {distance} points to\ncatch <PERSON><PERSON>!", "nextCustomCoinIn": "Only {distance} points to\ncatch Paws!", "saveMe": "SAVE ME!", "maxRevive": "Max Revive", "freeRevive": "Free Revive", "revive": " Revive", "end": "End"}, "lives": {"full": "Full", "moreLives": "More Lives", "noMoreLives": "No More Lives", "timeToNext": "Time to next life", "inviteFriendToGet": " Invite friend to get full lives!", "inviteFriend": "In<PERSON><PERSON>", "goToShop": "Go to Shop"}, "clans": {"title": "Clan", "clans": "Clans", "myClan": "My Clan", "topClans": "Top Clans", "event": {"name": "Clan Wars", "total_prize": "Total prize is\n{ton}", "description_1": "Participate in Clan Events\nand win up to {ton}", "requirenment": "Invite more friends\nto start the event"}}, "state": {"yourScore": "Your score", "ticketsCollected": "Tickets collected", "coinsCollected": "Coins collected"}, "connection": {"title": "Connection lost", "description": "Please check your Internet connection to continue playing"}, "longLoad": {"title": "Important!", "shortDescription": "Loading is taking longer than expected.", "fullDescription": "Please make sure you have a good network connection and kindly wait until all assets are loaded."}, "errors": {"appVersion": "Please update Telegram to the latest version", "walletNotConnectedForTransaction": "You need to connect your wallet before making a transaction"}, "warnings": {"inviteToCollect": "Invite more friends to collect"}, "contest": {"successCaption": "Congratulations! You are participating in the giveaway!", "failCaption": "You have not completed all tasks. Please read the giveaway conditions and try again!", "errorCaption": "Giveaway is not available.", "task": {"tickets": "Collect {value} tickets", "friends": "Invite {value} friends", "multiplier": "Reach multiplier of {value}", "skin": "Unlock \"{value}\" skin", "starsTotal": "Spent {value} stars in total", "starsDuringContest": "Spent {value} stars"}}, "instructions": {"hot_record": {"1": "Play Uni Jump", "2": "Reach<br/><span class=\"instruction__step-text_yellow\">MAX</span> Record", "3": "Earn <PERSON>", "4": "<span class=\"instruction__step-text_blue\">1000</span> best players<br/>will get <span class=\"instruction__step-text_yellow\">Stars</span>"}, "one_percent": {"1": "Play Uni Jump", "2": "Reach <span class=\"instruction__step-text_yellow\">Best</span> total<br/>score", "3": "Earn <PERSON>"}, "ton_mining": {"1": "Play Uni Jump", "2": "Collect TON", "3": "Use MAGNET for<br/>collect <span class=\"instruction__step-text_yellow\">X3</span> TON"}, "custom_coin": {"1": "Grab exclusive skins<br/>from limited-time boxes", "2": "Play and collect coins - each skin unlocks a better coin multiplier!", "3": "More coins — higher rank, better rewards!"}, "leagues": {"1": "Upgrade<br/><span class=\"instruction__step-text_yellow\">X</span> Multiplier", "2": "Collect more<br/>tickets", "3": "Unlock new <span class=\"instruction__step-text_yellow\">Leagues</span>", "4": "New <span class=\"instruction__step-text_yellow\">Leagues</span> unlock<br/>more Events!"}, "ice_fragment": {"1": "Play Uni Jump", "2": "Collect Coin", "3": "Get Re<PERSON><PERSON>!"}, "clan_create": {"1": "Create<br/>Telegram Group", "2": "Add <span class=\"instruction__step-text_yellow\">UniJump</span> bot", "3": "Give bot admin rights,<br/>as Admin", "4": "Invite friends to the group<br/>and grow the clan!"}, "clan_event": {"1": "Invite friends<br/>to the Clan", "2": "Reach <span class=\"instruction__step-text_yellow\">500</span> members<br/>of 3 League", "3": "Start<br/>the Event", "4": "Get <span class=\"instruction__step-text_blue\">50 TON</span> Reward!"}, "battle_event": {"1": "Choose your team", "2": "Play Uni Jump", "3": "Collect unique coin", "4": "Try to win and get<br/>more rewards"}}, "hoursFull": "Hours", "days": "d", "hours": "h", "minutes": "m", "minutesFull": "Minutes", "seconds": "s", "linkCopied": "Link copied", "error": "Error", "claimed": "Claimed", "canceled": "Canceled", "success": "Success", "pending": "Pending", "processing": "Processing"}