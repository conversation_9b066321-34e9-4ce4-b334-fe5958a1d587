import aim from '@/assets/images/temp/big-icons/aim.png'
import combatLootBox from '@/assets/images/temp/big-icons/combatBox.png'
import customCoin from '@/assets/images/temp/big-icons/custom-coin.png'
import hardImage from '@/assets/images/temp/big-icons/hard.png'
import jumper from '@/assets/images/temp/big-icons/jumper.png'
import { default as lives, default as livesImage } from '@/assets/images/temp/big-icons/lives.png'
import luckyLootBox from '@/assets/images/temp/big-icons/luckyBox.png'
import horn from '@/assets/images/temp/big-icons/magic-horn.png'
import magnetImage from '@/assets/images/temp/big-icons/magnet.png'
import rainbowLootBox from '@/assets/images/temp/big-icons/rainbowBox.png'
import refs from '@/assets/images/temp/big-icons/ref.png'
import softImage from '@/assets/images/temp/big-icons/soft.png'
import ticket from '@/assets/images/temp/big-icons/ticket.png'
import tonImageM from '@/assets/images/temp/big-icons/ton-m.png'
import unlimitedLivesImage from '@/assets/images/temp/big-icons/unlimited-lives-pile.png'
import wheelSpins from '@/assets/images/temp/big-icons/wheel-spin.png'
import dynamicCoin1 from '@/assets/images/temp/deep-dive/dynamic-coin-moon.png'
import dynamicCoin2 from '@/assets/images/temp/deep-dive/dynamic-coin-ocean.png'
import puzzleCoin from '@/assets/images/temp/fragment/coin.png'

import { usePlayerState } from '@/services/client/usePlayerState'
import type { Currency, RewardType } from '@/services/openapi'

// not global rewards like BattleEvent multiplier
// need just for special case
type SpecialRewards = 'multiplier'

export const REWARD_TO_IMAGE: Partial<Record<RewardType, string>> = {
  hard: hardImage,
  soft: softImage,
  ton: tonImageM,
  tickets: ticket,
  unlimitedLives: unlimitedLivesImage,
  fullLives: livesImage,
  timeBoundMagneticField: magnetImage,
  magicHorns: horn,
  luckyLootBox: luckyLootBox,
  rainbowLootBox: rainbowLootBox,
  combatLootBox: combatLootBox,
  stackableAimbot: aim,
  stackableJumper: jumper,
  stackableMagneticField: magnetImage,
  timeBoundAimbot: aim,
  timeBoundJumper: jumper,
  lives: lives,
  refsFake: refs,
  wheelSpins: wheelSpins,
  customCoin: customCoin,
  dynamicCoins: dynamicCoin1,
  puzzleCoins: puzzleCoin
}

export const REWARD_TO_IMAGE_CLASS: Partial<Record<RewardType | SpecialRewards, string>> = {
  hard: 'hard-coin-bg',
  soft: 'soft-coin-bg',
  tickets: 'ticket-bg',
  ton: 'ton-bg',
  magicHorns: 'horn-bg',
  customCoin: 'custom-coin-bg',
  wheelSpins: 'wheel-bg',
  refsFake: 'ref-bg',
  fullLives: 'heart-bg',
  lives: 'heart-bg',
  unlimitedLives: 'unlimited-heart-bg',
  stackableMagneticField: 'magnet-bg',
  stackableAimbot: 'aim-bg',
  stackableJumper: 'jumper-bg',
  timeBoundMagneticField: 'magnet-bg',
  timeBoundAimbot: 'aim-bg',
  timeBoundJumper: 'jumper-bg',
  puzzleCoins: 'puzzle-coin-bg',
  multiplier: 'multiplier-bg'
}

export const CURRENCY_TO_IMAGE_CLASS: Record<Currency, string> = {
  tickets: 'ticket-bg',
  hard: 'hard-coin-bg',
  soft: 'soft-coin-bg',
  ton: 'ton-bg',
  dynamicCoins: 'dynamic-coin-bg',
  magicHorns: 'horn-bg',
  stars: 'tg-star-bg',
  usd: 'dollar-bg',
  puzzleCoins: 'puzzle-coin-bg'
}

// use only if dynamic coins are used in your component as image
// otherwise use CONSTANT directly
export const useIconImage = () => {
  const { playerState } = usePlayerState()

  const getImage = (type: RewardType) => {
    if (type === 'dynamicCoins') {
      return playerState.value?.progressiveOffers?.find(offer => offer.usageDynamicCoins)?.id ===
        10000
        ? dynamicCoin1
        : dynamicCoin2
    }

    return REWARD_TO_IMAGE[type] ?? ''
  }

  const getImageClass = (type: RewardType | SpecialRewards) => {
    if (type === 'dynamicCoins') {
      return playerState.value?.progressiveOffers?.find(offer => offer.usageDynamicCoins)?.id ===
        10000
        ? 'dynamic-coin-bg_moon'
        : 'dynamic-coin-bg_ocean'
    }

    return REWARD_TO_IMAGE_CLASS[type]
  }

  const getCurrencyImageClass = (type: Currency) => {
    if (type === 'dynamicCoins') {
      return playerState.value?.progressiveOffers?.find(offer => offer.usageDynamicCoins)?.id ===
        10000
        ? 'dynamic-coin-bg_moon'
        : 'dynamic-coin-bg_ocean'
    }

    return CURRENCY_TO_IMAGE_CLASS[type]
  }

  return { getImage, getImageClass, getCurrencyImageClass }
}
